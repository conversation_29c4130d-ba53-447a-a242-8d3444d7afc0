# 支付时间功能实现

## 概述

为了完善支付回调处理，我们在UserRecharge表中新增了`payTime`字段来记录支付完成的时间，并完善了HaiPay等支付渠道的回调处理逻辑。

## 功能特性

1. **数据库字段扩展**：在`eb_user_recharge`表中新增`pay_time`字段
2. **HaiPay回调增强**：HaiPay支付回调支持提取和设置支付时间
3. **通用回调支持**：通用支付回调接口支持传递支付时间
4. **时间格式处理**：支持多种时间格式的转换和处理
5. **日志记录**：详细的支付时间处理日志

## 数据库变更

### 新增字段

```sql
-- 给 eb_user_recharge 表添加支付时间字段
ALTER TABLE `eb_user_recharge` 
ADD COLUMN `pay_time` timestamp(0) NULL DEFAULT NULL COMMENT '充值支付时间' AFTER `paid`;

-- 添加索引以提高查询性能
ALTER TABLE `eb_user_recharge` 
ADD INDEX `idx_pay_time` (`pay_time`) USING BTREE;
```

## 代码变更

### 1. 新增VO类

- `HaiPayCallbackVo.java` - HaiPay回调数据结构定义

### 2. 回调处理增强

#### HaiPay回调处理

```java
// 解析回调数据
HaiPayCallbackVo callbackVo = JSONObject.toJavaObject(jsonObject, HaiPayCallbackVo.class);

// 提取支付时间
String payTime = callbackVo.getPayTime();

// 设置支付时间到UserRecharge
if (StrUtil.isNotBlank(payTime)) {
    try {
        // 假设payTime是时间戳格式，需要转换为Date
        Date payTimeDate = new Date(Long.parseLong(payTime) * 1000);
        userRecharge.setPayTime(payTimeDate);
    } catch (Exception e) {
        // 如果转换失败，使用当前时间
        userRecharge.setPayTime(DateUtil.nowDateTime());
    }
} else {
    // 如果回调中没有支付时间，使用当前时间
    userRecharge.setPayTime(DateUtil.nowDateTime());
}
```

#### 通用回调处理

```java
// 设置支付时间（如果提供了的话）
if (userRecharge.getPayTime() != null) {
    recharge.setPayTime(userRecharge.getPayTime());
} else {
    // 如果没有提供支付时间，使用当前时间
    recharge.setPayTime(DateUtil.nowDateTime());
}
```

### 3. 控制器更新

- `CallbackController.java` - 通用支付回调支持传递支付时间

## HaiPay回调数据结构

### 回调数据格式

```json
{
  "orderId": "recharge123456",
  "orderNo": "haipay_order_789",
  "status": "1",
  "amount": "100.00",
  "payTime": "**********",
  "bankCode": "BCA",
  "bankNo": "014",
  "transactionId": "HAI123456789",
  "sign": "签名"
}
```

### 字段说明

- `orderId` - 订单ID
- `orderNo` - HaiPay订单号
- `status` - 支付状态（1表示成功）
- `amount` - 支付金额
- `payTime` - 支付时间（时间戳格式）
- `bankCode` - 银行代码
- `bankNo` - 银行号
- `transactionId` - 交易流水号
- `sign` - 签名

## 时间格式处理

### 支持的时间格式

1. **时间戳格式**：Unix时间戳（秒）
   ```java
   Date payTimeDate = new Date(Long.parseLong(payTime) * 1000);
   ```

2. **ISO格式**：标准ISO 8601格式
   ```java
   // 可以根据需要添加ISO格式解析
   ```

3. **自定义格式**：其他时间格式
   ```java
   // 可以根据需要添加自定义格式解析
   ```

### 时间处理策略

1. **优先使用回调时间**：如果回调中包含支付时间，优先使用
2. **格式转换失败**：如果时间格式转换失败，使用当前时间
3. **时间缺失**：如果回调中没有支付时间，使用当前时间
4. **日志记录**：记录时间处理的详细日志

## 使用方式

### 1. HaiPay支付回调

```bash
POST /api/admin/payment/callback/haipay
Content-Type: application/json

{
  "orderId": "recharge123456",
  "orderNo": "haipay_order_789",
  "status": "1",
  "payTime": "**********",
  "sign": "签名"
}
```

### 2. 通用支付回调

```bash
POST /api/admin/payment/callback/pay
Content-Type: application/json

{
  "orderNo": "recharge123456",
  "outTradeNo": "haipay_order_789",
  "payChannel": "haipay",
  "payTime": "2024-01-01T12:00:00Z"
}
```

## 配置说明

### 数据库配置

确保执行SQL脚本添加`pay_time`字段：

```bash
mysql -u username -p database_name < sql/add_pay_time_to_user_recharge.sql
```

### 应用配置

无需额外配置，功能会自动启用。

## 日志示例

### 支付时间设置日志

```
2024-01-01 12:00:00 INFO  - HaiPay支付回调：设置支付时间，订单号: recharge123456, 支付时间: 2024-01-01 12:00:00
2024-01-01 12:00:00 INFO  - 通用支付回调：设置支付时间，订单号: recharge123456, 支付时间: 2024-01-01 12:00:00
```

### 时间格式转换失败日志

```
2024-01-01 12:00:00 WARN  - HaiPay支付回调：支付时间格式转换失败，订单号: recharge123456, 支付时间: invalid_time
```

## 扩展指南

### 添加新的时间格式支持

1. 在`handleHaiPayRechargeCallback`方法中添加新的时间格式解析逻辑
2. 添加相应的异常处理和日志记录
3. 测试新的时间格式

### 添加其他支付渠道支持

1. 创建对应支付渠道的回调VO类
2. 在回调处理方法中添加支付时间提取逻辑
3. 更新相应的日志记录

## 注意事项

1. **时间格式**：确保了解各支付渠道的时间格式
2. **时区处理**：注意时区转换问题
3. **异常处理**：时间格式转换失败时要有合理的降级策略
4. **日志记录**：记录详细的时间处理日志便于问题排查
5. **数据一致性**：确保支付时间与支付状态的一致性 