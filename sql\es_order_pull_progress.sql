CREATE TABLE `es_order_pull_progress` (
  `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `platform` VARCHAR(32) NOT NULL DEFAULT 'tiktok' COMMENT '平台标识',
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NOT NULL,
  `batch_no` VARCHAR(64) NOT NULL,
  `last_page_no` INT NOT NULL DEFAULT 0,
  `last_page_token` VARCHAR(255) DEFAULT NULL,
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0-进行中 1-完成 2-失败',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` VARCHAR(255) DEFAULT NULL,
  UNIQUE KEY `uniq_platform_time_range_batch` (`platform`, `start_time`, `end_time`, `batch_no`),
  KEY `idx_platform_status_batch` (`platform`, `status`, `batch_no`),
  KEY `idx_platform_time_batch` (`platform`, `start_time`, `end_time`, `batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多平台订单分页任务生成进度表'; 