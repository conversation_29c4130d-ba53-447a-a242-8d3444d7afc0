<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.genco.service.dao.UserRechargeDao">

    <!-- 通用resultMap，补充pay_url字段映射 -->
    <resultMap id="BaseResultMap" type="com.genco.common.model.finance.UserRecharge">
        <id column="id" property="id" />
        <result column="uid" property="uid" />
        <result column="order_id" property="orderId" />
        <result column="price" property="price" />
        <result column="give_price" property="givePrice" />
        <result column="recharge_type" property="rechargeType" />
        <result column="paid" property="paid" />
        <result column="pay_time" property="payTime" />
        <result column="create_time" property="createTime" />
        <result column="refund_price" property="refundPrice" />
        <result column="out_trade_no" property="outTradeNo" />
        <result column="pay_channel" property="payChannel" />
        <result column="pay_url" property="payUrl" />
    </resultMap>

    <!--    根据类型获取该类型充值金额-->
    <select id="getSumByType" resultType="java.math.BigDecimal" parameterType="string">
        select sum(price) as price from eb_user_recharge
        where paid = 1
        <if test="type != null and type != '' ">
             and recharge_type = #{type, jdbcType=VARCHAR}
        </if>
    </select>
<!--    获取退款总额-->
    <select id="getSumByRefund" resultType="java.math.BigDecimal">
        select sum(refund_price) as price from eb_user_recharge
        where  refund_price > 0 and paid = 1
    </select>
</mapper>
