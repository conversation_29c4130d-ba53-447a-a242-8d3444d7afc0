package com.genco.service.service;

import com.genco.common.model.finance.UserRecharge;

/**
 * 订单支付回调 service
 */
public interface CallbackService {
    /**
     * 微信支付回调
     *
     * @param xmlInfo 微信回调json
     * @return String
     */
    String weChat(String xmlInfo);

    /**
     * 微信退款回调
     *
     * @param request 微信回调json
     * @return String
     */
    String weChatRefund(String request);


    /**
     * HaiPay支付回调
     *
     * @param request 回调json
     * @return String
     */
    String haiPay(String request);

    /**
     * 充值支付回调处理
     *
     * @param userRecharge 充值订单
     * @return Boolean
     */
    Boolean payRechargeCallback(UserRecharge userRecharge);
}
