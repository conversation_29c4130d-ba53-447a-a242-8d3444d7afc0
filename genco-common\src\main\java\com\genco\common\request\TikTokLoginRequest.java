package com.genco.common.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * TikTok第三方登录请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "TikTokLoginRequest对象", description = "TikTok第三方登录请求对象")
public class TikTokLoginRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @NotBlank(message = "token不能为空")
    @JsonProperty(value = "code")
    private String code;


    @ApiModelProperty(value = "推广人id")
    @JsonProperty(value = "spread_spid")
    private Integer spreadPid = 0;
}
