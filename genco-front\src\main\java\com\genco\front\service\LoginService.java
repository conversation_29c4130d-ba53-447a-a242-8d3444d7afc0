package com.genco.front.service;

import com.genco.common.model.user.User;
import com.genco.common.request.LoginMobileRequest;
import com.genco.common.request.LoginRequest;
import com.genco.common.response.LoginResponse;

import javax.servlet.http.HttpServletRequest;

/**
 * 移动端登录服务类
 */
public interface LoginService {

    /**
     * 账号密码登录
     *
     * @return LoginResponse
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 手机号验证码登录
     */
    LoginResponse phoneLogin(LoginMobileRequest loginRequest);

    /**
     * 老绑定分销关系
     *
     * @param user      User 用户user类
     * @param spreadUid Integer 推广人id
     * @return Boolean
     */
    Boolean bindSpread(User user, Integer spreadUid);

    /**
     * 推出登录
     *
     * @param request HttpServletRequest
     */
    void loginOut(HttpServletRequest request);


    /**
     * 使用Tiktok授权登录
     *
     * @param code      授权编码
     * @param spreadUid 推荐人
     * @param platform  平台（ios/android）
     * @return
     */
    LoginResponse tiktokAuthorizeLogin(String code, String codeVerifier, Integer spreadUid, String platform);
}
