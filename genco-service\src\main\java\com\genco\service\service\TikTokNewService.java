package com.genco.service.service;

import com.genco.common.token.TikTokOauthToken;

/**
 * TikTok公用服务
 */
public interface TikTokNewService {

    /**
     * 获取开放平台access_token
     * 通过 code 获取
     * 公众号使用
     *
     * @return 开放平台accessToken对象
     */
    TikTokOauthToken getOauth2AccessToken(String code, String codeVerifier, String platform);

    /**
     * https://developers.tiktok.com/doc/tiktok-api-v2-get-user-info
     * https://github.com/tiktok/tiktok-opensdk-ios/blob/main/LoginDemo/TikTokLoginDemo/ScopeEditViewController.swift
     * 文档这里似乎有点对不上，但是从官方的示例来看是这样写的，不知道哪个正确，也许都可以，只能实际测试试下了
     * 通过tiktok平台获取用户的账户信息
     * @param accessToken 通过getOauth2AccessToken获取到的参数
     * @param openId getOauth2AccessToken获取到用户参数
     * @return nickname
     */
    String getTiktokAccount(String accessToken, String openId);
}
