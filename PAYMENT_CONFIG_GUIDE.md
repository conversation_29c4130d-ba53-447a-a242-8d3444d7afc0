# 支付配置指南

## 概述

本文档说明如何配置各种支付方式的参数，包括微信支付、支付宝支付、Xendit支付和HaiPay支付。

## 微信支付配置

### 公众号支付
- `pay_weixin_appid`: 微信公众号AppID
- `pay_weixin_mch_id`: 微信商户号
- `pay_weixin_app_key`: 微信支付密钥

### 小程序支付
- `pay_routine_app_id`: 微信小程序AppID
- `pay_routine_mch_id`: 微信商户号
- `pay_routine_app_key`: 微信支付密钥

### 通用配置
- `site_url`: 网站域名
- `api_url`: API域名
- `site_name`: 网站名称

## 支付宝支付配置

### 基础配置
- `ali_pay_appid`: 支付宝应用ID
- `ali_pay_private_key`: 支付宝私钥
- `ali_pay_public_key`: 支付宝公钥
- `ali_pay_notifu_url`: 支付宝回调地址
- `ali_pay_return_url`: 支付宝返回地址
- `ali_pay_quit_url`: 支付宝退出地址

## Xendit支付配置

### 基础配置
- `xendit_api_key`: Xendit API密钥
- `xendit_callback_url`: Xendit回调地址
- `xendit_currency`: 货币类型（默认：IDR）
- `xendit_payment_methods`: 支持的支付方式

### 配置说明
1. 登录Xendit控制台获取API密钥
2. 设置回调地址用于接收支付结果通知
3. 配置支持的支付方式（如：OVO、DANA、GoPay等）

### 示例配置
```properties
xendit_api_key=your_xendit_api_key_here
xendit_callback_url=https://your-domain.com/api/admin/payment/callback/xendit
xendit_currency=IDR
xendit_payment_methods=OVO,DANA,GoPay
```

## HaiPay支付配置

### 基础配置
- `haipay_app_id`: HaiPay应用ID
- `haipay_bank_code`: HaiPay银行代码（如：PH_QRPH_DYNAMIC）
- `haipay_pay_type`: HaiPay支付类型（如：QR）
- `haipay_private_key`: HaiPay私钥（用于签名）
- `haipay_public_key`: HaiPay公钥（用于验签）
- `haipay_secret_key`: HaiPay密钥（用于签名）

### 配置说明
1. 登录HaiPay控制台获取应用ID和密钥
2. 配置RSA公私钥对用于签名验证
3. 设置银行代码和支付类型
4. 配置回调地址用于接收支付结果通知

### 示例配置
```properties
haipay_app_id=1054
haipay_bank_code=PH_QRPH_DYNAMIC
haipay_pay_type=QR
haipay_private_key=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCKqgxszb4ObuoJ5U8Ddz2ytOFFc81fmniRFo+jvNtrOegMUpd9gBPvMEaBzdeJC74Cg/HsAE7f9u24APLi0oLYZCwthhs01xTIhAis8h7IYCSdWrcbn8gpB7apSe4Ud0dS6zkuRcDTVGsLNrzNOMKHfT8S13dtsqDyDBBhSpkBwe9TkeLQB8K5ZjDYJ8uTtsrcey95eNFe22r8qAhv562yUBTG0PjZpQmiJWD0T6URLLzNYEOXEzsVVZgPKBNWSVZvMjLg32eFv/2Mw+x83n44w39FLx6E347A5hVQYWbBLuHDyFUzW/endOmMSj1YJmGDhctgEK+UIA1bNbexYo33AgMBAAECggEAZlZ6NRLjYeOZ9xO17OjkMDAu0gNVX2mx8eKkwENx7QEfsXiDNayBCdanMsWofQydf13B/lt72u9zIooQuDaFOw8zS6XeDnFudU582KcY8OmEHF4HJewW3bFDrk1R2OjvStMvsGbqmQ2EsxIC5bMuXrChDFbZXayn+/vLWwKjShetqPkN2cRHcKWaASqOnWOAnpgHm5VuGu2ttaR5K14pmMq7a0TOaj7lDYyHelWejCfqFFiWfYLefNj3oFVAfiNxwsxj8q42xWwPZ/Xzhn8p0cInja//1AMuNLIadyC4r6VR7cOIKm4F7XwCTCRCSmPbhDu5pOEA//pERFTTNtE7gQKBgQDdzbujAJRqkn0WwPtbKE7ZxR2KFjc4fM1LyPyODz4tbXhtXtZeMcjjsKn8pTpzbgj+Cfmhz9X8sKAqdxe1WJTtkgg5zbvPQ8A+Q0Su19LZMfFCuC0RCp1SX/asl4XeQe6fQZCft3AG7RgA5HjHET0/7Mpwb3C7A/xBwMfn51T0+wKBgQCgCuy9NmpG2bG/MEz1gDojYe08yKOGgTLp6v/UZcn+U6Oit37/sFe0vU7n9NMtkCLdhf2mqF1cNCUv+rzHkvtgG8FaNlsuozOMXuTNCJ6nj/IypMOnU8vV9DL9zUq5cUnny7HKwCTuS8FYZTjI75GfDDwrxIhhzOIkh2leQD+iNQKBgDV1xOgA18ToEeZOFUdfa8HpVLlXqW+gBQtjIhxLaD0iyYfy99A0R6s5hX8zg+cWemxgkx6BLZ5+I9yYX8qB00N/kyP7hmzqc4eORxutQVDATNo78gDNgiW8o4Pt8YIkehNAhk84s3O36bUtXD7+1Lh3pkN7WLx6tW5TvNsUUtHJAoGBAIYOoJ8dpYgTccAkRVKfRhO9Q2tW5SMVtgAayJCxcrGGfdseuVKT8+OBb0b83KedxJaqVf3zqcBCLaQy80543/dxSFS4k0hNjDBYjG7yeXMCMG4bdYgDuQpOsyfFfoI3UyDGjva2XDj/W8UfhKFLiz8ekIhY56SEaikPBEPerW7BAoGBAIW+5xD7BH4Z/w+GNrA5WFWNNH02+32AD/k6W59GQ+ejrFzCa9/SPa/7WEbBjKNWnzYl9pcdA0lP3LGEbKzrm6Zy+6lCHI6Hx/o4PbHaKTQg2jAIJdEUrAOKR44rjIY41a8wtgilfZA4I4zDSvJMPkMYOItIXjFCwHTxLLfw0CJp
haipay_public_key=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAiqoMbM2+Dm7qCeVPA3c9srThRXPNX5p4kRaPo7zbaznoDFKXfYAT7zBGgc3XiQu+AoPx7ABO3/btuADy4tKC2GQsLYYbNNcUyIQIrPIeyGAknVq3G5/IKQe2qUnuFHdHUus5LkXA01RrCza8zTjCh30/Etd3bbKg8gwQYUqZAcHvU5Hi0AfCuWYw2CfLk7bK3HsveXjRXttq/KgIb+etslAUxtD42aUJoiVg9E+lESy8zWBDlxM7FVWYDygTVklWbzIy4N9nhb/9jMPsfN5+OMN/RS8ehN+OwOYVUGFmwS7hw8hVM1v3p3TpjEo9WCZhg4XLYBCvlCANWzW3sWKN9wIDAQAB
haipay_secret_key=WFZflvcV75jYP2G6QIpSsMtoX1e4awxO
```

### API接口说明
- **请求地址**: `https://uat-interface.haipay.asia/php/collect/apply`
- **请求方法**: POST
- **Content-Type**: application/json
- **签名算法**: SHA256WithRSA
- **回调地址**: `/api/admin/payment/callback/haipay`

## 回调地址配置

### 微信支付回调
- 地址：`/api/admin/payment/callback/wechat`
- 方法：POST
- 格式：XML

### 支付宝回调
- 地址：`/api/admin/payment/callback/alipay`
- 方法：POST
- 格式：Form Data

### Xendit回调
- 地址：`/api/admin/payment/callback/xendit`
- 方法：POST
- 格式：JSON

### HaiPay回调
- 地址：`/api/admin/payment/callback/haipay`
- 方法：POST
- 格式：JSON

## 安全配置

### HTTPS要求
所有支付回调地址必须使用HTTPS协议，确保数据传输安全。

### 签名验证
- 微信支付：使用MD5或HMAC-SHA256签名
- 支付宝：使用RSA2签名
- Xendit：使用HMAC-SHA256签名
- HaiPay：使用MD5签名

### IP白名单
建议配置支付平台的IP白名单，只允许来自官方IP的回调请求。

## 测试环境配置

### 微信支付测试
- 使用微信支付沙箱环境
- 配置测试商户号和密钥
- 使用测试金额进行支付测试

### 支付宝测试
- 使用支付宝沙箱环境
- 配置测试应用ID和密钥
- 使用测试账号进行支付测试

### Xendit测试
- 使用Xendit沙箱环境
- 配置测试API密钥
- 使用测试支付方式进行测试

### HaiPay测试
- 使用HaiPay测试环境
- 配置测试API密钥和商户ID
- 使用测试支付方式进行测试

## 生产环境部署

### 配置检查清单
- [ ] 所有API密钥已正确配置
- [ ] 回调地址已正确设置
- [ ] HTTPS证书已安装
- [ ] 防火墙规则已配置
- [ ] 日志记录已启用
- [ ] 监控告警已设置

### 性能优化
- 配置连接池
- 启用缓存
- 设置超时时间
- 配置重试机制

### 监控告警
- 支付成功率监控
- 响应时间监控
- 错误率监控
- 资金流水监控

## 常见问题

### 1. 支付回调失败
- 检查回调地址是否正确
- 确认HTTPS证书有效
- 验证签名是否正确
- 检查防火墙设置

### 2. 支付超时
- 检查网络连接
- 调整超时时间
- 优化API调用
- 检查服务器性能

### 3. 签名验证失败
- 确认密钥配置正确
- 检查签名算法
- 验证请求参数
- 查看日志详情

## 联系支持

如果在配置过程中遇到问题，请联系技术支持：
- 微信支付：https://pay.weixin.qq.com/
- 支付宝：https://open.alipay.com/
- Xendit：https://www.xendit.co/
- HaiPay：https://haipay.com/ 