package com.genco.common.vo;

import lombok.Data;

/**
 * HaiPay支付请求VO
 */
@Data
public class HaiPayRequestVo {

    /**
     * 应用ID
     */
    private Long appId;

    /**
     * 订单ID
     */
    private String orderId;

    /**
     * 支付金额
     */
    private String amount;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 姓名
     */
    private String name;

    /**
     * 银行代码
     */
    private String inBankCode;

    /**
     * 支付类型
     */
    private String payType;


    /**
     * 支付后跳转地址
     */
    private String callBackUrl;

    /**
     * 用户唯一标识
     */
    private String partnerUserId;

    /**
     * 签名
     */
    private String sign;
} 