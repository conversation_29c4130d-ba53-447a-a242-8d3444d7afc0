package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.finance.UserRecharge;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.UserRechargeSearchRequest;
import com.genco.common.response.UserRechargeResponse;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * UserRechargeService 接口
 */
public interface UserRechargeService extends IService<UserRecharge> {

    /**
     * 充值记录列表
     *
     * @param request          请求参数
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<UserRechargeResponse> getList(UserRechargeSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 充值统计
     *
     * @return HashMap
     */
    HashMap<String, BigDecimal> getBalanceList();

    UserRecharge getInfoByEntity(UserRecharge userRecharge);

    /**
     * 根据日期获取充值订单数量
     *
     * @param date 日期，yyyy-MM-dd格式
     * @return Integer
     */
    Integer getRechargeOrderNumByDate(String date);

    /**
     * 根据日期获取充值订单金额
     *
     * @param date 日期，yyyy-MM-dd格式
     * @return BigDecimal
     */
    BigDecimal getRechargeOrderAmountByDate(String date);

    /**
     * 获取总人数
     *
     * @return Integer
     */
    Integer getTotalPeople();

    /**
     * 获取总金额
     *
     * @return BigDecimal
     */
    BigDecimal getTotalPrice();

    /**
     * 根据时间获取充值用户数量
     *
     * @param date 日期
     * @return Integer
     */
    Integer getRechargeUserNumByDate(String date);

    /**
     * 根据时间获取充值用户数量
     *
     * @param startDate 日期
     * @param endDate   日期
     * @return Integer
     */
    Integer getRechargeUserNumByPeriod(String startDate, String endDate);

    /**
     * 根据外部支付单号查询充值单
     *
     * @param outTradeNo 外部支付单号
     * @return UserRecharge
     */
    UserRecharge getByOutTradeNo(String outTradeNo);

    /**
     * 根据订单号查询充值单据
     *
     * @param orderNo
     * @return
     */
    UserRecharge getByOrderNo(String orderNo);

    /**
     * 根据支付渠道查询充值单列表
     *
     * @param payChannel 支付渠道
     * @return List<UserRecharge>
     */
    List<UserRecharge> getListByPayChannel(String payChannel);

    /**
     * 查询用户未支付的充值单
     *
     * @param uid          用户ID
     * @param rechargeType 充值类型
     * @return UserRecharge
     */
    UserRecharge getUnpaidRechargeByUserAndType(Integer uid, String rechargeType);
}
