package com.genco.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.genco.common.constants.TaskConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.finance.UserRecharge;
import com.genco.service.service.RechargePayService;
import com.genco.service.service.RechargeTaskService;
import com.genco.service.service.UserRechargeService;
import com.genco.common.utils.RedisUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 充值任务服务实现类
 */
@Service
public class RechargeTaskServiceImpl implements RechargeTaskService {

    private static final Logger logger = LoggerFactory.getLogger(RechargeTaskServiceImpl.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private RechargePayService rechargePayService;

    /**
     * 充值支付成功后置处理
     */
    @Override
    public void rechargePaySuccessAfter() {
        String redisKey = TaskConstants.RECHARGE_TASK_PAY_SUCCESS_AFTER;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("RechargeTaskServiceImpl.rechargePaySuccessAfter | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object data = redisUtil.getRightPop(redisKey, 10L);
            if (ObjectUtil.isNull(data)) {
                continue;
            }
            try {
                UserRecharge userRecharge = userRechargeService.getInfoByEntity(
                    new UserRecharge().setOrderId(String.valueOf(data))
                );
                if (ObjectUtil.isNull(userRecharge)) {
                    logger.error("RechargeTaskServiceImpl.rechargePaySuccessAfter | 充值订单不存在，orderNo: " + data);
                    throw new CrmebException("充值订单不存在，orderNo: " + data);
                }
                boolean result = rechargePayService.paySuccess(userRecharge);
                if (!result) {
                    redisUtil.lPush(redisKey, data);
                }
            } catch (Exception e) {
                redisUtil.lPush(redisKey, data);
            }
        }
    }
} 