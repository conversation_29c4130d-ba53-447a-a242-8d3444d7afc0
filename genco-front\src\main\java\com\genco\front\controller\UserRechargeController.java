package com.genco.front.controller;

import com.genco.common.constants.Constants;
import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.UserRechargeRequest;
import com.genco.common.response.CommonResult;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.UserRechargeBillRecordResponse;
import com.genco.common.response.UserRechargeFrontResponse;
import com.genco.common.utils.CrmebUtil;
import com.genco.front.service.UserCenterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户 -- 充值
 */
@Slf4j
@RestController("UserRechargeController")
@RequestMapping("api/front/recharge")
@Api(tags = "用户 -- 充值")
public class UserRechargeController {
    @Autowired
    private UserCenterService userCenterService;

    /**
     * 充值额度选择
     */
    @ApiOperation(value = "充值额度选择")
    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public CommonResult<UserRechargeFrontResponse> getRechargeConfig() {
        return CommonResult.success(userCenterService.getRechargeConfig());
    }

    /**
     * 小程序充值
     */
    @ApiOperation(value = "小程序充值")
    @RequestMapping(value = "/routine", method = RequestMethod.POST)
    public CommonResult<Map<String, Object>> routineRecharge(HttpServletRequest httpServletRequest, @RequestBody @Validated UserRechargeRequest request) {
        request.setRechargeType(Constants.PAY_TYPE_WE_CHAT_FROM_PROGRAM);
        request.setClientIp(CrmebUtil.getClientIp(httpServletRequest));
        OrderPayResultResponse recharge = userCenterService.recharge(request);
        Map<String, Object> map = new HashMap<>();
        map.put("data", recharge);
        map.put("type", request.getRechargeType());
        return CommonResult.success(map);
    }

    /**
     * 充值下单
     */
    @ApiOperation(value = "充值下单")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public CommonResult<OrderPayResultResponse> createRecharge(HttpServletRequest httpServletRequest, @RequestBody @Validated UserRechargeRequest request) {
        request.setClientIp(CrmebUtil.getClientIp(httpServletRequest));
        return CommonResult.success(userCenterService.recharge(request));
    }


    /**
     * 用户账单记录
     */
    @ApiOperation(value = "用户账单记录")
    @RequestMapping(value = "/bill/record", method = RequestMethod.GET)
    @ApiImplicitParam(name = "type", value = "记录类型：all-全部，expenditure-支出，income-收入", required = true)
    public CommonResult<CommonPage<UserRechargeBillRecordResponse>> billRecord(@RequestParam(name = "type") String type, @ModelAttribute PageParamRequest pageRequest) {
        return CommonResult.success(userCenterService.nowMoneyBillRecord(type, pageRequest));
    }
}



