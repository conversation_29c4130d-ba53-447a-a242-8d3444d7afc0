# 系统奖励政策独立性完整分析

## 概述

GENCO电商系统采用了**完全分离和独立执行**的奖励政策架构。每个奖励政策都有独立的触发机制、处理逻辑、配置参数和账单类型，可以独立开启/关闭，互不影响。

## 政策架构设计

### 核心组件

1. **UserRewardTask**: 统一的任务实体，支持多种任务类型
2. **UserRewardTaskProcessor**: 处理器接口，每个政策有独立实现
3. **UserRewardTaskScheduler**: 统一的任务调度器
4. **SystemConfig**: 配置管理，每个政策有独立配置

### 任务流程

```
业务触发 → 创建任务 → 任务入库 → 调度器拉取 → 分发处理器 → 执行奖励逻辑 → 更新状态
```

## 五大独立奖励政策详细分析

### 1. 订单返佣政策 (ORDER_REWARD)

#### 基本信息
- **任务类型**: `TASK_TYPE_ORDER_REWARD`
- **处理器**: `OrderRewardProcessor`
- **触发时机**: TikTok订单状态变为SETTLED（已结算）时
- **账单类型**: `order_cashback`

#### 触发机制
```java
// TiktokOrderSyncServiceImpl.nextByOrderStatus()
if (nextStatus == 6) { // SETTLED 已结算
    userRewardTaskService.createOrderRewardTask(storeOrder.getUid(), storeOrder.getOrderId(),
            null, JSONUtil.toJsonStr(storeOrder));
}
```

#### 奖励逻辑
1. **三级返现结构**：
   - 当前用户（一级）：`订单金额 × 平台返现率 × 用户返现率`
   - 二级推广人：`订单金额 × 平台返现率 × 二级返现率`
   - 三级推广人：`订单金额 × 平台返现率 × 三级返现率`

2. **用户等级区分**：
   - 普通用户（level=0）：使用 `NORMAL_CASH_BACK_RATE`
   - 代理用户（level=1-3）：使用 `AGENT_CASH_BACK_RATE`
   - 合作伙伴（level=4-6）：使用 `PARTNER_CASH_BACK_RATE`

#### 配置参数
```java
// 平台返现率
public static final String PLATFORM_CASH_BACK_RATE = "platform_cash_back_rate";
// 普通用户返现率
public static final String NORMAL_CASH_BACK_RATE = "normal_cash_back_rate";
// 代理返现率
public static final String AGENT_CASH_BACK_RATE = "agent_cash_back_rate";
// 合作伙伴返现率
public static final String PARTNER_CASH_BACK_RATE = "partner_cash_back_rate";
```

#### 处理流程
1. 查询订单和用户信息
2. 获取推广链路（最多三级）
3. 计算各级返现金额
4. 创建UserBill账单记录
5. 更新用户余额

---

### 2. 邀请奖励政策 (INVITE_REWARD)

#### 基本信息
- **任务类型**: `TASK_TYPE_INVITE_REWARD`
- **处理器**: `InviteRewardProcessor`
- **触发时机**: 用户绑定邀请码时
- **账单类型**: `invite_reward_1l`, `invite_reward_2l`, `invite_reward_3l`

#### 触发机制
```java
// UserServiceImpl.bindInviteCode()
userRewardTaskService.createInviteRewardTask(currentUser.getUid(), inviter.getUid(), null);
```

#### 奖励逻辑
1. **会员等级矩阵**：基于邀请人和被邀请人的会员等级（0-4）组合
2. **三级奖励结构**：
   - 一级奖励：直接邀请人获得
   - 二级奖励：邀请人的上级获得
   - 三级奖励：邀请人的上上级获得

3. **配置键生成规则**：
   ```java
   // 一级奖励配置键
   String key = String.format("member_invite_reward_l1_%d_%d", inviterGrade, inviteeGrade);
   // 二级奖励配置键  
   String key = String.format("member_invite_reward_l2_%d", inviterGrade);
   // 三级奖励配置键
   String key = String.format("member_invite_reward_l3_%d", inviterGrade);
   ```

#### 配置参数
```java
// 邀请奖励功能开关
public static final String MEMBER_INVITE_REWARD_ENABLE = "member_invite_reward_enable";
// 25个一级奖励配置（5×5矩阵）
member_invite_reward_l1_0_0, member_invite_reward_l1_0_1, ...
// 5个二级奖励配置
member_invite_reward_l2_0, member_invite_reward_l2_1, ...
// 5个三级奖励配置
member_invite_reward_l3_0, member_invite_reward_l3_1, ...
```

#### 处理流程
1. 检查邀请奖励功能开关
2. 获取邀请人和被邀请人信息
3. 获取双方会员等级
4. 查找推广链路（最多三级）
5. 根据等级矩阵计算奖励金额
6. 创建奖励账单并更新余额

---

### 3. 分销佣金政策

#### 基本信息
- **处理方式**: 直接处理（非任务模式）
- **处理方法**: `assignCommission()`
- **触发时机**: 订单支付成功时
- **记录类型**: `UserBrokerageRecord`

#### 触发机制
```java
// OrderPayServiceImpl.afterPaySuccess()
List<UserBrokerageRecord> recordList = assignCommission(storeOrder);
```

#### 奖励逻辑
1. **二级分销结构**：
   - 一级分销：直接推广人获得佣金
   - 二级分销：推广人的上级获得佣金

2. **佣金计算公式**：
   ```
   佣金 = 商品佣金价格 × 分销比例
   ```

3. **排除条件**：
   - 营销产品不参与（拼团、秒杀、砍价）
   - 用户无上级或上级是自己
   - 分销功能未开启

#### 配置参数
```java
// 分销功能开关
public static final String CONFIG_KEY_STORE_BROKERAGE_IS_OPEN = "brokerage_func_status";
// 一级分销比例
public static final String CONFIG_KEY_STORE_BROKERAGE_RATE_ONE = "store_brokerage_statu";
// 二级分销比例  
public static final String CONFIG_KEY_STORE_BROKERAGE_RATE_TWO = "store_brokerage_two";
// 分销员门槛
public static final String CONFIG_KEY_STORE_BROKERAGE_QUOTA = "store_brokerage_price";
```

#### 处理流程
1. 检查分销功能开关
2. 验证订单类型（排除营销产品）
3. 获取用户推广链路
4. 计算各级佣金金额
5. 创建佣金记录
6. 更新推广人佣金余额

---

### 4. 代理奖励政策 (AGENT_REWARD)

#### 基本信息
- **任务类型**: `TASK_TYPE_AGENT_REWARD`
- **处理器**: `AgentRewardProcessor`
- **触发时机**: 代理/合作伙伴注册时
- **账单类型**: `agent_invite_reward`, `member_agent_reward`

#### 触发机制
```java
// 用户升级为代理/合作伙伴时触发
userRewardTaskService.createAgentRewardTask(userId, inviteUid, contextJson);
```

#### 奖励逻辑
1. **双模式支持**：
   - 原有代理/合作伙伴模式（level 1-6）
   - 新会员等级模式（grade 0-4）

2. **三级奖励结构**：
   - 一级奖励：直接推广人获得
   - 二级奖励：推广人的上级获得  
   - 三级奖励：推广人的上上级获得

3. **模式切换**：
   ```java
   String memberModeConfig = systemConfigService.getValueByKey("use_member_level_mode");
   if ("1".equals(memberModeConfig)) {
       processMemberLevelReward(task, user);  // 新模式
   } else {
       processLegacyAgentReward(task, user);  // 原模式
   }
   ```

#### 配置参数

**原模式配置**：
```java
// 代理注册奖励
public static final String CONFIG_KEY_AGENT_REGISTER_REWARD_1L = "agent_register_reward_1l";
public static final String CONFIG_KEY_AGENT_REGISTER_REWARD_2L = "agent_register_reward_2l";
public static final String CONFIG_KEY_AGENT_REGISTER_REWARD_3L = "agent_register_reward_3l";
// 合作伙伴注册奖励
public static final String CONFIG_KEY_PARTNER_REGISTER_REWARD_1L = "partner_register_reward_1l";
public static final String CONFIG_KEY_PARTNER_REGISTER_REWARD_2L = "partner_register_reward_2l";
public static final String CONFIG_KEY_PARTNER_REGISTER_REWARD_3L = "partner_register_reward_3l";
```

**新模式配置**：
```java
// 会员注册奖励（基于等级）
member_register_reward_l1_0, member_register_reward_l1_1, ...
member_register_reward_l2_0, member_register_reward_l2_1, ...
member_register_reward_l3_0, member_register_reward_l3_1, ...
```

#### 处理流程
1. 获取注册用户信息
2. 判断使用哪种模式
3. 获取推广链路（最多三级）
4. 根据模式和等级计算奖励
5. 创建奖励账单并更新余额

---

### 5. 首单邀请奖励政策 (FIRST_ORDER_INVITE_REWARD) - 新增

#### 基本信息
- **任务类型**: `TASK_TYPE_FIRST_ORDER_INVITE_REWARD`
- **处理器**: `FirstOrderInviteRewardProcessor`（待实现）
- **触发时机**: 被邀请人首笔TikTok订单结算时
- **账单类型**: `invite_first_order`

#### 触发机制
```java
// TiktokOrderSyncServiceImpl.nextByOrderStatus() - 新增逻辑
if (nextStatus == 6) { // SETTLED 已结算
    // 检查是否为用户首单，如果是则创建首单邀请奖励任务
    checkAndCreateFirstOrderInviteReward(storeOrder);
}
```

#### 奖励逻辑
1. **统一奖励**：所有等级会员获得相同固定金额
2. **首单检测**：验证是否为用户首笔TikTok订单
3. **防重复**：确保每个邀请关系只奖励一次
4. **模式切换**：支持绑定即奖励和首单后奖励两种模式

#### 配置参数
```java
// 首单邀请奖励功能开关
public static final String FIRST_ORDER_INVITE_REWARD_ENABLE = "first_order_invite_reward_enable";
// 首单邀请奖励金额（统一固定金额）
public static final String FIRST_ORDER_INVITE_REWARD_AMOUNT = "first_order_invite_reward_amount";
// 首单邀请奖励模式（0-绑定即奖励，1-首单完成后奖励）
public static final String FIRST_ORDER_INVITE_REWARD_MODE = "first_order_invite_reward_mode";
```

#### 处理流程
1. 检查首单奖励功能开关
2. 验证是否为用户首笔TikTok订单
3. 获取邀请关系信息
4. 检查是否已发放过首单奖励
5. 发放统一固定金额奖励
6. 标记已发放状态

## 政策独立性验证

### 实际场景分析

**场景**：用户A（金牌会员）邀请用户B（普通会员），B完成首笔TikTok订单（金额100元）

#### 可能触发的奖励（并行执行）：

1. **邀请奖励** - B绑定邀请码时
   - A获得：根据金牌邀请普通会员的配置金额
   - A的上级获得：二级邀请奖励
   - A的上上级获得：三级邀请奖励

2. **分销佣金** - B支付订单时
   - A获得：商品佣金价格 × 一级分销比例
   - A的上级获得：商品佣金价格 × 二级分销比例

3. **订单返佣** - B订单结算时
   - B获得：100元 × 平台返现率 × 普通用户返现率
   - A获得：100元 × 平台返现率 × 二级返现率
   - A的上级获得：100元 × 平台返现率 × 三级返现率

4. **首单奖励** - B首单结算时（新规则）
   - A获得：固定5元（配置值）

### 独立性特征

1. **独立触发**：每个政策有不同的触发时机和条件
2. **独立配置**：每个政策有独立的开关和参数
3. **独立处理**：通过不同的处理器独立执行
4. **独立账单**：使用不同的账单类型便于区分
5. **并行运行**：多个政策可同时生效，互不冲突
6. **独立维护**：修改一个政策不影响其他政策

## 技术优势

1. **高内聚低耦合**：每个政策逻辑集中，相互独立
2. **易于扩展**：新增政策只需添加新的处理器
3. **配置灵活**：每个政策可独立开启/关闭
4. **测试友好**：可单独测试每个政策逻辑
5. **维护简单**：问题定位和修复更加精准
6. **性能优化**：可针对不同政策进行独立优化

## 总结

GENCO系统的奖励政策架构设计充分体现了**分离关注点**的设计原则。每个奖励政策都是独立的业务模块，有清晰的边界和职责。这种设计不仅提高了系统的可维护性和扩展性，也为业务运营提供了极大的灵活性。

新增的首单邀请奖励政策将完美融入这个架构，与现有政策并行运行，实现业务需求的同时保持技术架构的优雅性。
