package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 首页商品对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "IndexProductResponse对象", description = "首页商品对象")
public class IndexProductResponse {

    @ApiModelProperty(value = "商品id")
    private Integer id;

    @ApiModelProperty(value = "门店名称")
    private String shopName;

    @ApiModelProperty(value = "商品名称")
    private String title;

    @ApiModelProperty(value = "商品图片")
    private String mainImageUrl;

    @ApiModelProperty(value = "详情页地址")
    private String detailLink;

    @ApiModelProperty(value = "售卖价格")
    private BigDecimal salesPrice;

    @ApiModelProperty(value = "最低售卖价格")
    private BigDecimal minSalesPrice;

    @ApiModelProperty(value = "最高售卖价格")
    private BigDecimal maxSalesPrice;

    @ApiModelProperty(value = "原始价格")
    private BigDecimal oriPrice;

    @ApiModelProperty(value = "最低原始价格")
    private BigDecimal minOriPrice;

    @ApiModelProperty(value = "最高原始价格")
    private BigDecimal maxOriPrice;

    @ApiModelProperty(value = "商品渠道")
    private String channel;

    @ApiModelProperty(value = "返现率")
    private BigDecimal cashBackRate;

    @ApiModelProperty(value = "返现金额")
    private BigDecimal cashBackAmount;

    @ApiModelProperty(value = "历史销量")
    private Integer unitsSold;

    @ApiModelProperty(value = "单位名")
    private String unitName;

    @ApiModelProperty(value = "是否有库存")
    private Integer hasInventory;
}
