# 充值下单重复预防功能

## 功能概述

在用户充值下单时，系统会检查是否存在未支付的同类型充值单。如果存在，则返回之前的单据号而不是创建新的充值单，避免重复创建充值订单。

## 实现逻辑

### 1. 数据库查询方法

在 `UserRechargeService` 接口中添加了新的查询方法：

```java
/**
 * 查询用户未支付的充值单
 *
 * @param uid 用户ID
 * @param rechargeType 充值类型
 * @return UserRecharge
 */
UserRecharge getUnpaidRechargeByUserAndType(Integer uid, String rechargeType);
```

### 2. 实现逻辑

在 `UserRechargeServiceImpl` 中实现该方法：

```java
@Override
public UserRecharge getUnpaidRechargeByUserAndType(Integer uid, String rechargeType) {
    LambdaQueryWrapper<UserRecharge> lambdaQueryWrapper = new LambdaQueryWrapper<>();
    lambdaQueryWrapper.eq(UserRecharge::getUid, uid);
    lambdaQueryWrapper.eq(UserRecharge::getRechargeType, rechargeType);
    lambdaQueryWrapper.eq(UserRecharge::getPaid, false);
    lambdaQueryWrapper.orderByDesc(UserRecharge::getCreateTime);
    lambdaQueryWrapper.last("LIMIT 1");
    return dao.selectOne(lambdaQueryWrapper);
}
```

### 3. 充值下单流程修改

在 `UserCenterServiceImpl.recharge()` 方法中添加检查逻辑：

```java
@Override
@Transactional(rollbackFor = {RuntimeException.class, Error.class, CrmebException.class})
public OrderPayResultResponse recharge(UserRechargeRequest request) {
    // ... 验证逻辑 ...
    
    User currentUser = userService.getInfoException();
    
    // 检查是否存在未支付的同类型充值单
    UserRecharge existingRecharge = userRechargeService.getUnpaidRechargeByUserAndType(
        currentUser.getUid(), request.getRechargeType());
    
    if (existingRecharge != null) {
        // 如果存在未支付的同类型充值单，直接返回该充值单信息
        logger.info("用户{}存在未支付的同类型充值单，订单号：{}，充值类型：{}", 
            currentUser.getUid(), existingRecharge.getOrderId(), request.getRechargeType());
        
        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(existingRecharge.getOrderId());
        response.setAmount(existingRecharge.getPrice());
        response.setStatus(true);
        response.setPayType(request.getPayType());
        return response;
    }
    
    // 如果不存在未支付充值单，则创建新的充值单
    // ... 创建新充值单的逻辑 ...
}
```

## 功能特点

### 1. 同类型检查
- 只检查相同充值类型（如 `agent`、`partner`）的未支付充值单
- 不同充值类型之间不会相互影响

### 2. 用户隔离
- 只检查当前用户的未支付充值单
- 不同用户之间的充值单相互独立

### 3. 时间优先
- 如果有多个未支付充值单，返回最新创建的一个
- 通过 `orderByDesc(UserRecharge::getCreateTime)` 和 `LIMIT 1` 实现

### 4. 日志记录
- 当发现未支付充值单时，会记录详细的日志信息
- 便于后续的问题排查和数据分析

## 使用场景

### 1. 防止重复下单
用户在网络不稳定或操作失误时，可能会多次点击充值按钮，系统会返回同一个未支付的充值单号。

### 2. 支付中断恢复
如果用户在支付过程中中断了支付流程，再次发起充值请求时，系统会返回之前的充值单，用户可以继续支付。

### 3. 多端同步
用户在不同设备上发起相同类型的充值请求时，系统会返回同一个充值单，避免重复创建。

## 测试用例

### 1. 正常场景测试
- 用户首次充值：创建新的充值单
- 用户重复充值同类型：返回之前的未支付充值单

### 2. 边界场景测试
- 用户充值不同类型：创建新的充值单
- 不同用户充值：各自创建独立的充值单
- 已支付充值单：不影响新充值单的创建

### 3. 异常场景测试
- 数据库连接异常：抛出相应的异常信息
- 并发充值请求：通过数据库事务保证数据一致性

## 注意事项

1. **事务处理**：整个充值下单流程使用 `@Transactional` 注解保证数据一致性
2. **日志记录**：重要操作都有相应的日志记录，便于问题排查
3. **性能考虑**：查询时使用了索引字段（uid、rechargeType、paid），保证查询效率
4. **扩展性**：如果后续需要支持更多充值类型，现有逻辑无需修改

## 相关文件

- `UserRechargeService.java` - 服务接口
- `UserRechargeServiceImpl.java` - 服务实现
- `UserCenterServiceImpl.java` - 充值下单逻辑
- `UserRecharge.java` - 充值单实体类
- `UserRechargeServiceTest.java` - 测试类 