package com.genco.service.model.tiktok;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 订单拉取任务消息体（用于Redis队列分发）
 * 包含平台、时间区间、批次号、页码、nextPageToken等信息
 */
@Data
public class OrderPullTaskMessage implements Serializable {
    /** 平台标识（如 tiktok、shopee） */
    private String platform;
    /** 拉取订单的起始时间 */
    private Date startTime;
    /** 拉取订单的结束时间 */
    private Date endTime;
    /** 拉取批次号/轮次号 */
    private String batchNo;
    /** 当前页码 */
    private Integer pageNo;
    /** 分页token（用于API请求下一页） */
    private String nextPageToken;
    // 可扩展其它字段
} 