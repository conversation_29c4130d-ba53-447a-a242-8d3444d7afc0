package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.genco.common.model.user.UserBrokerageRecord;
import com.genco.common.request.BrokerageRecordRequest;
import com.genco.common.request.PageParamRequest;
import com.genco.common.vo.UserFundsMonitor;

/**
 * UserRechargeService 接口
 */
public interface UserFundsMonitorService extends IService<UserFundsMonitor> {

    /**
     * 佣金记录
     *
     * @param request          筛选条件
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<UserBrokerageRecord> getBrokerageRecord(BrokerageRecordRequest request, PageParamRequest pageParamRequest);
}
