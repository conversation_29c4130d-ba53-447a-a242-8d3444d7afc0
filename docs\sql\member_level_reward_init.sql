-- 会员等级邀请奖励系统初始化脚本
-- 用于将系统从代理/合作伙伴模式迁移到会员等级模式

-- 1. 插入会员等级配置数据（如果不存在）
INSERT IGNORE INTO `eb_system_user_level` (`id`, `name`, `experience`, `grade`, `discount`, `icon`, `status`, `is_del`, `add_time`) VALUES
(1, '普通用户', 0, 0, 100, '', 1, 0, NOW()),
(2, '铜牌会员', 100, 1, 95, '', 1, 0, NOW()),
(3, '银牌会员', 500, 2, 90, '', 1, 0, NOW()),
(4, '金牌会员', 1500, 3, 85, '', 1, 0, NOW()),
(5, '钻石会员', 5000, 4, 80, '', 1, 0, NOW());

-- 2. 插入会员等级模式配置
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('会员等级模式', 'use_member_level_mode', '1', 1, '是否使用会员等级模式（1-是，0-否，使用代理/合作伙伴模式）', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value` = '1', `update_time` = NOW();

-- 3. 插入会员邀请奖励总开关配置
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('会员邀请奖励开关', 'member_invite_reward_enable', '1', 1, '会员邀请奖励功能开关（1-开启，0-关闭）', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value` = '1', `update_time` = NOW();

-- 4. 插入会员等级邀请奖励配置（一级奖励 - 根据邀请人和被邀请人等级）
-- 0级会员邀请奖励
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('0级会员邀请0级奖励', 'member_level_0_invite_level_0_reward_1l', '1.00', 1, '0级会员邀请0级会员一级奖励金额', NOW(), NOW()),
('0级会员邀请1级奖励', 'member_level_0_invite_level_1_reward_1l', '2.00', 1, '0级会员邀请1级会员一级奖励金额', NOW(), NOW()),
('0级会员邀请2级奖励', 'member_level_0_invite_level_2_reward_1l', '3.00', 1, '0级会员邀请2级会员一级奖励金额', NOW(), NOW()),
('0级会员邀请3级奖励', 'member_level_0_invite_level_3_reward_1l', '5.00', 1, '0级会员邀请3级会员一级奖励金额', NOW(), NOW()),
('0级会员邀请4级奖励', 'member_level_0_invite_level_4_reward_1l', '10.00', 1, '0级会员邀请4级会员一级奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 1级会员邀请奖励
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('1级会员邀请0级奖励', 'member_level_1_invite_level_0_reward_1l', '2.00', 1, '1级会员邀请0级会员一级奖励金额', NOW(), NOW()),
('1级会员邀请1级奖励', 'member_level_1_invite_level_1_reward_1l', '5.00', 1, '1级会员邀请1级会员一级奖励金额', NOW(), NOW()),
('1级会员邀请2级奖励', 'member_level_1_invite_level_2_reward_1l', '8.00', 1, '1级会员邀请2级会员一级奖励金额', NOW(), NOW()),
('1级会员邀请3级奖励', 'member_level_1_invite_level_3_reward_1l', '12.00', 1, '1级会员邀请3级会员一级奖励金额', NOW(), NOW()),
('1级会员邀请4级奖励', 'member_level_1_invite_level_4_reward_1l', '20.00', 1, '1级会员邀请4级会员一级奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 2级会员邀请奖励
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('2级会员邀请0级奖励', 'member_level_2_invite_level_0_reward_1l', '3.00', 1, '2级会员邀请0级会员一级奖励金额', NOW(), NOW()),
('2级会员邀请1级奖励', 'member_level_2_invite_level_1_reward_1l', '8.00', 1, '2级会员邀请1级会员一级奖励金额', NOW(), NOW()),
('2级会员邀请2级奖励', 'member_level_2_invite_level_2_reward_1l', '15.00', 1, '2级会员邀请2级会员一级奖励金额', NOW(), NOW()),
('2级会员邀请3级奖励', 'member_level_2_invite_level_3_reward_1l', '25.00', 1, '2级会员邀请3级会员一级奖励金额', NOW(), NOW()),
('2级会员邀请4级奖励', 'member_level_2_invite_level_4_reward_1l', '40.00', 1, '2级会员邀请4级会员一级奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 3级会员邀请奖励
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('3级会员邀请0级奖励', 'member_level_3_invite_level_0_reward_1l', '5.00', 1, '3级会员邀请0级会员一级奖励金额', NOW(), NOW()),
('3级会员邀请1级奖励', 'member_level_3_invite_level_1_reward_1l', '12.00', 1, '3级会员邀请1级会员一级奖励金额', NOW(), NOW()),
('3级会员邀请2级奖励', 'member_level_3_invite_level_2_reward_1l', '25.00', 1, '3级会员邀请2级会员一级奖励金额', NOW(), NOW()),
('3级会员邀请3级奖励', 'member_level_3_invite_level_3_reward_1l', '50.00', 1, '3级会员邀请3级会员一级奖励金额', NOW(), NOW()),
('3级会员邀请4级奖励', 'member_level_3_invite_level_4_reward_1l', '80.00', 1, '3级会员邀请4级会员一级奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 4级会员邀请奖励
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('4级会员邀请0级奖励', 'member_level_4_invite_level_0_reward_1l', '10.00', 1, '4级会员邀请0级会员一级奖励金额', NOW(), NOW()),
('4级会员邀请1级奖励', 'member_level_4_invite_level_1_reward_1l', '20.00', 1, '4级会员邀请1级会员一级奖励金额', NOW(), NOW()),
('4级会员邀请2级奖励', 'member_level_4_invite_level_2_reward_1l', '40.00', 1, '4级会员邀请2级会员一级奖励金额', NOW(), NOW()),
('4级会员邀请3级奖励', 'member_level_4_invite_level_3_reward_1l', '80.00', 1, '4级会员邀请3级会员一级奖励金额', NOW(), NOW()),
('4级会员邀请4级奖励', 'member_level_4_invite_level_4_reward_1l', '150.00', 1, '4级会员邀请4级会员一级奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 5. 插入二级奖励配置（上级的上级获得的奖励）
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('0级会员二级奖励', 'member_level_0_invite_reward_2l', '0.50', 1, '0级会员二级邀请奖励金额', NOW(), NOW()),
('1级会员二级奖励', 'member_level_1_invite_reward_2l', '1.00', 1, '1级会员二级邀请奖励金额', NOW(), NOW()),
('2级会员二级奖励', 'member_level_2_invite_reward_2l', '2.00', 1, '2级会员二级邀请奖励金额', NOW(), NOW()),
('3级会员二级奖励', 'member_level_3_invite_reward_2l', '5.00', 1, '3级会员二级邀请奖励金额', NOW(), NOW()),
('4级会员二级奖励', 'member_level_4_invite_reward_2l', '10.00', 1, '4级会员二级邀请奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 6. 插入三级奖励配置（上级的上级的上级获得的奖励）
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('0级会员三级奖励', 'member_level_0_invite_reward_3l', '0.20', 1, '0级会员三级邀请奖励金额', NOW(), NOW()),
('1级会员三级奖励', 'member_level_1_invite_reward_3l', '0.50', 1, '1级会员三级邀请奖励金额', NOW(), NOW()),
('2级会员三级奖励', 'member_level_2_invite_reward_3l', '1.00', 1, '2级会员三级邀请奖励金额', NOW(), NOW()),
('3级会员三级奖励', 'member_level_3_invite_reward_3l', '2.00', 1, '3级会员三级邀请奖励金额', NOW(), NOW()),
('4级会员三级奖励', 'member_level_4_invite_reward_3l', '5.00', 1, '4级会员三级邀请奖励金额', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 7. 更新现有用户的会员等级（根据经验值）
-- 注意：这个脚本需要根据实际业务逻辑调整
UPDATE `eb_user` u 
LEFT JOIN `eb_system_user_level` sul ON (
    u.experience >= sul.experience 
    AND sul.status = 1 
    AND sul.is_del = 0
)
SET u.level = (
    SELECT MAX(id) FROM `eb_system_user_level` 
    WHERE experience <= u.experience 
    AND status = 1 
    AND is_del = 0
)
WHERE u.level IS NULL OR u.level = 0;

-- 8. 创建用户等级记录表数据（如果需要）
INSERT IGNORE INTO `eb_user_level` (`uid`, `level_id`, `grade`, `status`, `mark`, `add_time`)
SELECT 
    u.uid,
    u.level,
    sul.grade,
    1,
    '系统初始化会员等级',
    NOW()
FROM `eb_user` u
LEFT JOIN `eb_system_user_level` sul ON u.level = sul.id
WHERE u.level IS NOT NULL AND u.level > 0;

-- 9. 添加系统配置说明
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('会员等级说明', 'member_level_description', '会员等级系统：0级-普通用户，1级-铜牌会员，2级-银牌会员，3级-金牌会员，4级-钻石会员', 1, '会员等级系统说明', NOW(), NOW())
ON DUPLICATE KEY UPDATE `update_time` = NOW();

-- 10. 记录迁移日志
INSERT INTO `eb_system_config` (`menu_name`, `key`, `value`, `status`, `info`, `create_time`, `update_time`) VALUES
('系统迁移记录', 'member_level_migration_date', NOW(), 1, '会员等级系统迁移完成时间', NOW(), NOW())
ON DUPLICATE KEY UPDATE `value` = NOW(), `update_time` = NOW();

COMMIT;

-- 迁移完成提示
SELECT '会员等级邀请奖励系统初始化完成！' as message;
SELECT '请检查以下配置：' as notice;
SELECT 'use_member_level_mode = 1 (启用会员等级模式)' as config1;
SELECT 'member_invite_reward_enable = 1 (启用会员邀请奖励)' as config2;
SELECT '会员等级配置已插入到 eb_system_user_level 表' as config3;
SELECT '邀请奖励配置已插入到 eb_system_config 表' as config4;
