package com.genco.service.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.genco.common.constants.Constants;
import com.genco.common.constants.TaskConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.model.order.StoreOrderInfo;
import com.genco.common.model.order.StoreOrderStatus;
import com.genco.common.model.product.StoreProductReply;
import com.genco.common.model.user.User;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.RedisUtil;
import com.genco.common.vo.StoreOrderInfoOldVo;
import com.genco.service.service.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.SearchCreatorAffiliateOrdersResponseDataOrdersSkus;

import java.math.BigDecimal;
import java.util.List;

/**
 * StoreOrderServiceImpl 接口实现
 */
@Service
public class OrderTaskServiceImpl implements OrderTaskService {
    //日志
    private static final Logger logger = LoggerFactory.getLogger(OrderTaskServiceImpl.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StoreOrderTaskService storeOrderTaskService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreOrderStatusService storeOrderStatusService;

    @Autowired
    private StoreOrderInfoService storeOrderInfoService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreProductReplyService storeProductReplyService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private OrderPayService orderPayService;

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 用户取消订单
     */
    @Override
    public void cancelByUser() {
        String redisKey = Constants.ORDER_TASK_REDIS_KEY_AFTER_CANCEL_BY_USER;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("OrderTaskServiceImpl.cancelByUser | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object data = redisUtil.getRightPop(redisKey, 10L);
            if (null == data) {
                continue;
            }
            try {
                StoreOrder storeOrder = storeOrderService.getById(Integer.valueOf(data.toString()));
                boolean result = storeOrderTaskService.cancelByUser(storeOrder);
                if (!result) {
                    redisUtil.lPush(redisKey, data);
                }
            } catch (Exception e) {
                redisUtil.lPush(redisKey, data);
            }
        }
    }

    private StoreOrder getJavaBeanStoreOrder(Object data) {
        return JSONObject.toJavaObject(JSONObject.parseObject(data.toString()), StoreOrder.class);
    }

    /**
     * 执行 用户退款申请
     */
    @Override
    public void refundApply() {
        String redisKey = Constants.ORDER_TASK_REDIS_KEY_AFTER_REFUND_BY_USER;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("OrderTaskServiceImpl.refundApply | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object orderId = redisUtil.getRightPop(redisKey, 10L);
            if (null == orderId) {
                continue;
            }
            try {
                StoreOrder storeOrder = storeOrderService.getById(Integer.valueOf(orderId.toString()));
                if (ObjectUtil.isNull(storeOrder)) {
                    throw new CrmebException("订单不存在,orderNo = " + orderId);
                }
                boolean result = storeOrderTaskService.refundOrder(storeOrder);
                if (!result) {
                    logger.error("订单退款错误：result = " + result);
                    redisUtil.lPush(redisKey, orderId);
                }
            } catch (Exception e) {
                logger.error("订单退款错误：" + e.getMessage());
                redisUtil.lPush(redisKey, orderId);
            }
        }
    }

    /**
     * 完成订单
     */
    @Override
    public void complete() {
        String redisKey = Constants.ORDER_TASK_REDIS_KEY_AFTER_COMPLETE_BY_USER;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("OrderTaskServiceImpl.complete | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object data = redisUtil.getRightPop(redisKey, 10L);
            if (null == data) {
                continue;
            }
            try {
                StoreOrder storeOrder = getJavaBeanStoreOrder(data);
                boolean result = storeOrderTaskService.complete(storeOrder);
                if (!result) {
                    redisUtil.lPush(redisKey, data);
                }
            } catch (Exception e) {
                redisUtil.lPush(redisKey, data);
            }
        }
    }

    /**
     * 订单支付成功后置处理
     */
    @Override
    public void orderPaySuccessAfter() {
        String redisKey = TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("OrderTaskServiceImpl.orderPaySuccessAfter | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object data = redisUtil.getRightPop(redisKey, 10L);
            if (ObjectUtil.isNull(data)) {
                continue;
            }
            try {
                StoreOrder storeOrder = storeOrderService.getByOderId(String.valueOf(data));
                if (ObjectUtil.isNull(storeOrder)) {
                    logger.error("OrderTaskServiceImpl.orderPaySuccessAfter | 订单不存在，orderNo: " + data);
                    throw new CrmebException("订单不存在，orderNo: " + data);
                }
                boolean result = orderPayService.paySuccess(storeOrder);
                if (!result) {
                    redisUtil.lPush(redisKey, data);
                }
            } catch (Exception e) {
                redisUtil.lPush(redisKey, data);
            }
        }
    }

    /**
     * 自动取消未支付订单
     */
    @Override
    public void autoCancel() {
        String redisKey = Constants.ORDER_AUTO_CANCEL_KEY;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("OrderTaskServiceImpl.autoCancel | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object data = redisUtil.getRightPop(redisKey, 10L);
            if (null == data) {
                continue;
            }
            try {
                StoreOrder storeOrder = storeOrderService.getByOderId(String.valueOf(data));
                if (ObjectUtil.isNull(storeOrder)) {
                    logger.error("OrderTaskServiceImpl.autoCancel | 订单不存在，orderNo: " + data);
                    throw new CrmebException("订单不存在，orderNo: " + data);
                }
                boolean result = storeOrderTaskService.autoCancel(storeOrder);
                if (!result) {
                    redisUtil.lPush(redisKey, data);
                }
            } catch (Exception e) {
                e.printStackTrace();
                redisUtil.lPush(redisKey, data);
            }
        }
    }

    /**
     * 订单收货
     */
    @Override
    public void orderReceiving() {
        String redisKey = TaskConstants.ORDER_TASK_REDIS_KEY_AFTER_TAKE_BY_USER;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("OrderTaskServiceImpl.orderReceiving | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            //如果10秒钟拿不到一个数据，那么退出循环
            Object id = redisUtil.getRightPop(redisKey, 10L);
            if (null == id) {
                continue;
            }
            try {
                Boolean result = storeOrderTaskService.orderReceiving(Integer.valueOf(id.toString()));
                if (!result) {
                    redisUtil.lPush(redisKey, id);
                }
            } catch (Exception e) {
                redisUtil.lPush(redisKey, id);
            }
        }
    }

    /**
     * 订单自动完成
     */
    @Override
    public void autoComplete() {
        // 查找所有收获状态订单
        List<StoreOrder> orderList = storeOrderService.findIdAndUidListByReceipt();
        if (CollUtil.isEmpty(orderList)) {
            return;
        }
        logger.info("OrderTaskServiceImpl.autoComplete | size:0");

        // 根据订单状态表判断订单是否可以自动完成
        for (StoreOrder order : orderList) {
            StoreOrderStatus orderStatus = storeOrderStatusService.getLastByOrderId(order.getId());
            if (!"user_take_delivery".equals(orderStatus.getChangeType())) {
                logger.error("订单自动完成：订单记录最后一条不是收货状态，orderId = " + order.getId());
                continue;
            }
            // 判断是否到自动完成时间（收货时间向后偏移7天）
            String comTime = DateUtil.addDay(orderStatus.getCreateTime(), 7, Constants.DATE_FORMAT);
            int compareDate = DateUtil.compareDate(comTime, DateUtil.nowDateTime(Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (compareDate < 0) {
                continue;
            }

            // 获取订单详情
            List<StoreOrderInfoOldVo> orderInfoVoList = storeOrderInfoService.getOrderListByOrderId(order.getId());
            if (CollUtil.isEmpty(orderInfoVoList)) {
                logger.error("订单自动完成：无订单详情数据，orderId = " + order.getId());
                continue;
            }
            List<StoreProductReply> replyList = CollUtil.newArrayList();
            User user = userService.getById(order.getUid());
            // 生成评论
            for (StoreOrderInfoOldVo orderInfo : orderInfoVoList) {
                // 判断是否已评论
                if (orderInfo.getInfo().getIsReply().equals(1)) {
                    continue;
                }
                String replyType = Constants.STORE_REPLY_TYPE_PRODUCT;
                StoreProductReply reply = new StoreProductReply();
                reply.setUid(order.getUid());
                reply.setOid(order.getId());
                reply.setProductId(orderInfo.getProductId());
                reply.setUnique(orderInfo.getUnique());
                reply.setReplyType(replyType);
                reply.setProductScore(5);
                reply.setServiceScore(5);
                reply.setComment("");
                reply.setPics("");
                reply.setNickname(user.getNickname());
                reply.setAvatar(user.getAvatar());
                reply.setSku(orderInfo.getInfo().getSku());
                reply.setCreateTime(DateUtil.nowDateTime());
                replyList.add(reply);
            }
            order.setStatus(Constants.ORDER_STATUS_INT_COMPLETE);
            Boolean execute = transactionTemplate.execute(e -> {
                storeOrderService.updateById(order);
                storeProductReplyService.saveBatch(replyList);
                return Boolean.TRUE;
            });
            if (execute) {
                redisUtil.lPush(Constants.ORDER_TASK_REDIS_KEY_AFTER_COMPLETE_BY_USER, order.getId());
            } else {
                logger.error("订单自动完成：更新数据库失败，orderId = " + order.getId());
            }
        }
    }

    private StoreOrderInfo generateStoreOrderInfo(String orderNo, SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku
            , BigDecimal totalPrice) {
        StoreOrderInfo storeOrderInfo = new StoreOrderInfo();
        storeOrderInfo.setInfo("-");
        storeOrderInfo.setUnique(orderNo);
        storeOrderInfo.setSku("-");
        storeOrderInfo.setImage("-");
        storeOrderInfo.setWeight(new BigDecimal(0));
        storeOrderInfo.setVolume(new BigDecimal(0));
        storeOrderInfo.setGiveIntegral(0);
        storeOrderInfo.setVipPrice(new BigDecimal(0));
        storeOrderInfo.setOrderNo(orderNo);
        storeOrderInfo.setTag(sku.getTag());
        storeOrderInfo.setContentId(sku.getContentId());
        storeOrderInfo.setPayNum(sku.getQuantity());
        storeOrderInfo.setOutProductId(sku.getProductId());
        storeOrderInfo.setCampaignId(sku.getCampaignId());
        storeOrderInfo.setProductName(sku.getProductName());
        storeOrderInfo.setProductType(5);
        storeOrderInfo.setShopName(sku.getShopName());
        storeOrderInfo.setRefundedQuantity(sku.getRefundedQuantity());
        storeOrderInfo.setReturnedQuantity(sku.getReturnedQuantity());
        //价格不能为空
        if (sku.getPrice() != null && StringUtils.isNotEmpty(sku.getPrice().getAmount())) {
            storeOrderInfo.setPrice(new BigDecimal(StringUtils.substring(sku.getPrice().getAmount(), 2)));
            totalPrice = totalPrice.add(storeOrderInfo.getPrice());
        }
        storeOrderInfo.setPayNum(sku.getQuantity());
        if (sku.getActualBonusCommission() != null && StringUtils.isNotEmpty(sku.getActualBonusCommission().getAmount())) {
            storeOrderInfo.setActualBonusCommission(new BigDecimal(StringUtils.substring(sku.getActualBonusCommission().getAmount(), 2)));
        }
        if (sku.getActualCommission() != null && StringUtils.isNotEmpty(sku.getActualCommission().getAmount())) {
            storeOrderInfo.setActualCommission(new BigDecimal(StringUtils.substring(sku.getActualCommission().getAmount(), 2)));
        }
        if (sku.getActualCommissionBase() != null && StringUtils.isNotEmpty(sku.getActualCommissionBase().getAmount())) {
            storeOrderInfo.setActualCommissionBase(new BigDecimal(StringUtils.substring(sku.getActualCommissionBase().getAmount(), 2)));
        }
        if (sku.getActualCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getActualCreatorCommissionRewardFee().getAmount())) {
            storeOrderInfo.setActualCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getActualCreatorCommissionRewardFee().getAmount(), 2)));
        }
        if (sku.getActualBonusCommission() != null && StringUtils.isNotEmpty(sku.getActualBonusCommission().getAmount())) {
            storeOrderInfo.setActualBonusCommission(new BigDecimal(StringUtils.substring(sku.getActualBonusCommission().getAmount(), 2)));
        }
        if (sku.getCommissionBonusRate() != null) {
            storeOrderInfo.setCommissionBonusRate(new BigDecimal(sku.getCommissionBonusRate()));
        }
        if (sku.getEstimatedBonusCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedBonusCommission().getAmount())) {
            storeOrderInfo.setEstimatedBonusCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedBonusCommission().getAmount(), 2)));
        }
        if (sku.getEstimatedCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedCommission().getAmount())) {
            storeOrderInfo.setEstimatedCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedCommission().getAmount(), 2)));
        }
        if (sku.getEstimatedShopAdsCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedShopAdsCommission().getAmount())) {
            storeOrderInfo.setEstimatedShopAdsCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedShopAdsCommission().getAmount(), 2)));
        }
        if (sku.getEstimatedCommissionBase() != null && StringUtils.isNotEmpty(sku.getEstimatedCommissionBase().getAmount())) {
            storeOrderInfo.setEstimatedCommissionBase(new BigDecimal(StringUtils.substring(sku.getEstimatedCommissionBase().getAmount(), 2)));
        }
        if (sku.getEstimatedCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getEstimatedCreatorCommissionRewardFee().getAmount())) {
            storeOrderInfo.setEstimatedCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getEstimatedCreatorCommissionRewardFee().getAmount(), 2)));
        }
        if (sku.getShopAdsCommissionRate() != null) {
            storeOrderInfo.setShopAdsCommissionRate(sku.getShopAdsCommissionRate());
        }
        return storeOrderInfo;
    }
}
