package com.genco.service.service;

import com.genco.common.enums.ProductChannelEnum;

/**
 * TikTok产品ID相关服务
 */
public interface TikTokProductIdService {

    /**
     * 解析URL地址 识别具体平台
     *
     * @param url URL地址
     * @return 所属平台链接
     */
    ProductChannelEnum identifyPlatform(String url);

    /**
     * tiktok 产品ID解析
     *
     * @param url url地址
     * @return 链接中的产品ID
     */
    String extractProductId(String url);

}
