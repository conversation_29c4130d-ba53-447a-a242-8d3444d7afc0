package com.genco.admin.task.order;

import com.genco.common.constants.Constants;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.RedisUtil;
import com.genco.service.service.OrderTaskService;
import com.genco.service.service.TiktokOrderSyncService;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

public class OrderTiktokAsyncTask {

    //日志
    private static final Logger logger = LoggerFactory.getLogger(OrderCompleteTask.class);

    @Autowired
    private OrderTaskService orderTaskService;

    @Autowired
    private TiktokOrderSyncService tiktokOrderSyncService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 初始化需要拉取的队列
     * <p>
     * 按天初始化任务，每天写入同步一笔数据
     */
    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    public void init() {
        String redisKey = Constants.ORDER_ASYNC_BY_DAY_REDIS_KEY;
        String nextPageToken = null;
        try {
            // 执行一页数据的刷新
            nextPageToken = tiktokOrderSyncService.syncTiktokOrders(null, null, null);
            // 如果还有下一页，则继续往下一页走
            if (StringUtils.isNotEmpty(nextPageToken)) {
                redisUtil.lPush(redisKey, nextPageToken);
            }
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(nextPageToken)) {
                redisUtil.lPush(redisKey, nextPageToken);
            }
        }
    }

    /**
     * 执行每一页订单的刷新动作
     */
    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    public void doAsync() {
        logger.info("---OrderTiktokAsyncTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            String redisKey = Constants.ORDER_ASYNC_PAGE_TOKEN_REDIS_KEY;
            Long size = redisUtil.getListSize(redisKey);
            logger.info("OrderTiktokAsyncTask.pageToken | size:" + size);
            if (size < 1) {
                return;
            }
            for (int i = 0; i < size; i++) {
                //如果10秒钟拿不到一个数据，那么退出循环
                String pageToken = (String) redisUtil.getRightPop(redisKey, 10L);
                if (StringUtils.isNotEmpty(pageToken)) {
                    continue;
                }
                try {
                    // 执行一页数据的刷新
                    String nextPageToken = tiktokOrderSyncService.syncTiktokOrders(pageToken, null, null);
                    // 如果还有下一页，则继续往下一页走
                    if (StringUtils.isNotEmpty(nextPageToken)) {
                        redisUtil.lPush(redisKey, nextPageToken);
                    }
                } catch (Exception e) {
                    redisUtil.lPush(redisKey, pageToken);
                }
            }

        } catch (Exception e) {
            logger.error("OrderTiktokAsyncTask.task" + " | msg : " + e.getMessage());
        }

    }
}
