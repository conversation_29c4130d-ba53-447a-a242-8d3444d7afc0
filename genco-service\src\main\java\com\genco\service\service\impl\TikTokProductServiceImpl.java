package com.genco.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.request.StoreProductAddRequest;
import com.genco.common.request.StoreProductRequest;
import com.genco.common.response.StoreProductInfoResponse;
import com.genco.common.response.StoreProductResponse;
import com.genco.service.dao.StoreProductDao;
import com.genco.service.service.TikTokProductService;
import org.json.JSONException;

import java.io.IOException;
import java.util.Collections;
import java.util.List;

public class TikTokProductServiceImpl extends ServiceImpl<StoreProductDao, StoreProduct> implements TikTokProductService {
    @Override
    public List<StoreProduct> getListInIds(List<Integer> productIds) {
        return Collections.emptyList();
    }

    @Override
    public StoreProductResponse getByProductId(Integer productId) {
        return null;
    }

    @Override
    public Boolean save(StoreProductAddRequest request) {
        return null;
    }

    @Override
    public Boolean update(StoreProductAddRequest storeProductRequest) {
        return null;
    }

    @Override
    public StoreProductInfoResponse getInfo(Integer id) {
        return null;
    }

    @Override
    public StoreProductRequest importProductFromUrl(String url, int tag) throws IOException, JSONException {
        return null;
    }

    @Override
    public Boolean deleteProduct(Integer productId, String type) {
        return null;
    }
}
