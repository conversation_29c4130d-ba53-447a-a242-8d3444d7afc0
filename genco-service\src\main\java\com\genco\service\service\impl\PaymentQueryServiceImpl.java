package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.PayConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.finance.UserRecharge;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.service.service.PaymentQueryService;
import com.genco.service.service.PaymentStrategy;
import com.genco.service.service.StoreOrderService;
import com.genco.service.service.UserRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 支付查询服务实现类
 */
@Slf4j
@Service
public class PaymentQueryServiceImpl implements PaymentQueryService {

    @Autowired
    private PaymentStrategyFactory paymentStrategyFactory;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserRechargeService userRechargeService;

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        log.info("查询支付结果开始，订单号：{}", orderNo);

        try {
            if (StrUtil.isBlank(orderNo)) {
                throw new CrmebException("订单号不能为空");
            }

            // 根据订单号前缀判断业务类型
            String bizType = "unknown";
            if (orderNo.startsWith("wxNo") || orderNo.startsWith("order")) {
                bizType = PayConstants.BIZ_TYPE_ORDER;
            } else if (orderNo.startsWith("recharge")) {
                bizType = PayConstants.BIZ_TYPE_RECHARGE;
            }

            // 根据业务类型查询订单信息，获取支付渠道
            String payChannel = null;
            if (PayConstants.BIZ_TYPE_ORDER.equals(bizType)) {
                StoreOrder storeOrder = storeOrderService.getByOderId(orderNo);
                if (storeOrder != null) {
                    payChannel = storeOrder.getPayType();
                }
            } else if (PayConstants.BIZ_TYPE_RECHARGE.equals(bizType)) {
                UserRecharge userRecharge = userRechargeService.getInfoByEntity(
                        new UserRecharge().setOrderId(orderNo)
                );
                if (userRecharge != null) {
                    payChannel = userRecharge.getPayChannel();
                }
            }

            // 如果没有找到支付渠道，尝试根据外部支付单号查询
            if (StrUtil.isBlank(payChannel)) {
                if (PayConstants.BIZ_TYPE_RECHARGE.equals(bizType)) {
                    UserRecharge userRecharge = userRechargeService.getByOutTradeNo(orderNo);
                    if (userRecharge != null) {
                        payChannel = userRecharge.getPayChannel();
                        orderNo = userRecharge.getOrderId(); // 使用内部订单号
                    }
                }
            }

            // 如果仍然没有找到支付渠道，返回错误
            if (StrUtil.isBlank(payChannel)) {
                throw new CrmebException("未找到订单对应的支付渠道信息");
            }

            // 使用对应的支付策略查询
            return queryPaymentResult(orderNo, payChannel);

        } catch (Exception e) {
            log.error("查询支付结果异常，订单号：{}", orderNo, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo, String payChannel) {
        log.info("根据支付渠道查询支付结果开始，订单号：{}，支付渠道：{}", orderNo, payChannel);

        try {
            if (StrUtil.isBlank(orderNo)) {
                throw new CrmebException("订单号不能为空");
            }

            if (StrUtil.isBlank(payChannel)) {
                throw new CrmebException("支付渠道不能为空");
            }

            // 检查是否支持该支付渠道
            if (!paymentStrategyFactory.supports(payChannel)) {
                throw new CrmebException("不支持的支付渠道：" + payChannel);
            }

            // 获取对应的支付策略
            PaymentStrategy strategy = paymentStrategyFactory.getStrategy(payChannel);

            // 调用策略的查询方法
            PaymentQueryResultResponse result = strategy.queryPaymentResult(orderNo);

            log.info("查询支付结果成功，订单号：{}，支付渠道：{}，支付状态：{}", orderNo, payChannel, result.getPaid());
            return result;

        } catch (Exception e) {
            log.error("根据支付渠道查询支付结果异常，订单号：{}，支付渠道：{}", orderNo, payChannel, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }
} 