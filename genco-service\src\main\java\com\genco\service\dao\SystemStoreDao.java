package com.genco.service.dao;

import com.genco.common.request.StoreNearRequest;
import com.genco.common.vo.SystemStoreNearVo;
import com.genco.common.model.system.SystemStore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 门店自提 Mapper 接口
 */
public interface SystemStoreDao extends BaseMapper<SystemStore> {

    List<SystemStoreNearVo> getNearList(StoreNearRequest request);
}

