package com.genco.admin.task.order;

import com.alibaba.fastjson.JSON;
import com.genco.common.utils.RedisUtil;
import com.genco.service.model.tiktok.OrderPullTask;
import com.genco.service.model.tiktok.OrderPullTaskMessage;
import com.genco.service.service.OrderPullTaskService;
import com.genco.service.service.PlatformOrderSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 订单拉取任务消费端（基于Redis队列）
 */
@Component
@EnableScheduling
public class OrderPullTaskConsumer {

    private static final Logger logger = LoggerFactory.getLogger(OrderPullTaskConsumer.class);

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private List<PlatformOrderSyncService> platformOrderSyncServices;

    @Autowired
    private OrderPullTaskService orderPullTaskService;

    /**
     * 每5秒消费一次队列
     */
    @Scheduled(fixedDelay = 5000)
    public void consumeTaskQueue() {
        // 支持多平台
        String[] platforms = {"tiktok", "shopee"};
        for (String platform : platforms) {
            String queueKey = "order_pull_task_queue:" + platform;
            String msgStr = (String) redisUtil.getRightPop(queueKey, 5L);
            while (msgStr != null) {
                try {
                    OrderPullTaskMessage msg = JSON.parseObject(msgStr, OrderPullTaskMessage.class);
                    // 抢占任务表（CAS更新status=0/3->1）
                    boolean locked = orderPullTaskService.lambdaUpdate()
                            .set(OrderPullTask::getStatus, 1)
                            .set(OrderPullTask::getLastPullTime, new Date())
                            .eq(OrderPullTask::getPlatform, msg.getPlatform())
                            .eq(OrderPullTask::getStartTime, msg.getStartTime())
                            .eq(OrderPullTask::getEndTime, msg.getEndTime())
                            .eq(OrderPullTask::getBatchNo, msg.getBatchNo())
                            .eq(OrderPullTask::getPageNo, msg.getPageNo())
                            .in(OrderPullTask::getStatus, 0, 3)
                            .update();
                    if (!locked) {
                        logger.info("任务已被抢占或状态不对，跳过: {}", msg);
                        msgStr = (String) redisUtil.getRightPop(queueKey, 5L);
                        continue;
                    }
                    // 路由到对应平台的同步实现
                    PlatformOrderSyncService syncService = platformOrderSyncServices.stream()
                            .filter(s -> s.getPlatform().equalsIgnoreCase(platform))
                            .findFirst().orElse(null);
                    if (syncService == null) {
                        logger.error("未找到平台同步实现: {}", platform);
                        msgStr = (String) redisUtil.getRightPop(queueKey, 5L);
                        continue;
                    }
                    // 分布式锁：对订单号加锁（假设nextPageToken唯一标识本页订单）
                    String lockKey = "lock:order_pull:" + platform + ":" + msg.getBatchNo() + ":" + msg.getPageNo();
                    boolean gotLock = redisUtil.set(lockKey, "1", 60L); // 60秒锁
                    if (!gotLock) {
                        // 未获取到锁，重新入队
                        redisUtil.lPush(queueKey, msgStr);
                        logger.warn("未获取到分布式锁，任务重新入队: {}", msg);
                        msgStr = (String) redisUtil.getRightPop(queueKey, 5L);
                        continue;
                    }
                    try {
                        // 调用同步
                        String nextPageToken = syncService.syncOrders(msg.getNextPageToken(), msg.getStartTime(), msg.getEndTime());
                        // 处理分页：如有下一页，生成新任务
                        if (StringUtils.hasText(nextPageToken)) {
                            OrderPullTaskMessage nextMsg = new OrderPullTaskMessage();
                            nextMsg.setPlatform(msg.getPlatform());
                            nextMsg.setStartTime(msg.getStartTime());
                            nextMsg.setEndTime(msg.getEndTime());
                            nextMsg.setBatchNo(msg.getBatchNo());
                            nextMsg.setPageNo(msg.getPageNo() + 1);
                            nextMsg.setNextPageToken(nextPageToken);
                            String nextQueueKey = "order_pull_task_queue:" + msg.getPlatform();
                            redisUtil.lPush(nextQueueKey, JSON.toJSONString(nextMsg));
                        }
                        // 标记当前任务成功
                        orderPullTaskService.lambdaUpdate()
                                .set(OrderPullTask::getStatus, 2)
                                .set(OrderPullTask::getUpdateTime, new Date())
                                .eq(OrderPullTask::getPlatform, msg.getPlatform())
                                .eq(OrderPullTask::getStartTime, msg.getStartTime())
                                .eq(OrderPullTask::getEndTime, msg.getEndTime())
                                .eq(OrderPullTask::getBatchNo, msg.getBatchNo())
                                .eq(OrderPullTask::getPageNo, msg.getPageNo())
                                .update();
                    } catch (Exception e) {
                        logger.error("订单拉取任务消费失败: {}", msgStr, e);
                        // 失败重试：任务重新入队，任务表重试次数+1，状态置为3
                        redisUtil.lPush(queueKey, msgStr);
                        orderPullTaskService.lambdaUpdate()
                                .set(OrderPullTask::getStatus, 3)
                                .set(OrderPullTask::getRetryCount, (Integer) null)
                                .setSql("retry_count = retry_count + 1")
                                .set(OrderPullTask::getUpdateTime, new Date())
                                .eq(OrderPullTask::getPlatform, msg.getPlatform())
                                .eq(OrderPullTask::getStartTime, msg.getStartTime())
                                .eq(OrderPullTask::getEndTime, msg.getEndTime())
                                .eq(OrderPullTask::getBatchNo, msg.getBatchNo())
                                .eq(OrderPullTask::getPageNo, msg.getPageNo())
                                .update();
                    } finally {
                        redisUtil.delete(lockKey);
                    }
                } catch (Exception e) {
                    logger.error("订单拉取任务消费异常: {}", msgStr, e);
                }
                msgStr = (String) redisUtil.getRightPop(queueKey, 5L);
            }
        }
    }
} 