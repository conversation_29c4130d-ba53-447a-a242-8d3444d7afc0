package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import com.genco.common.constants.PayConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.user.User;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.vo.*;
import com.genco.service.service.HaiPayService;
import com.genco.service.service.PaymentStrategy;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * HaiPay支付策略实现
 */
@Slf4j
@Service
public class HaiPayPaymentStrategy implements PaymentStrategy {

    private static final Logger logger = LoggerFactory.getLogger(HaiPayPaymentStrategy.class);

    @Autowired
    private HaiPayService haiPayService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserService userService;

    @Override
    public OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest) {
        logger.info("HaiPay支付开始处理，订单号：{}", paymentRequest.getOrderNo());

        try {
            // 获取HaiPay配置
            Long appId = Long.valueOf(systemConfigService.getValueByKey("haipay_app_id"));
            String apiUrl = systemConfigService.getValueByKey("haipay_api_url");
            String inBankCode = systemConfigService.getValueByKey("haipay_in_bank_code");
            String callBackUrl = systemConfigService.getValueByKey("haipay_return_url");

            if (StrUtil.isBlank(inBankCode) || StrUtil.isBlank(apiUrl)) {
                throw new CrmebException("HaiPay配置不完整，请联系管理员");
            }

            if (!paymentRequest.getPayType().equals("QR")) {
                throw new CrmebException("HaiPay 目前只支持QR支付类型");
            }

            // 获取用户信息
            User user = userService.getById(paymentRequest.getUid());
            if (user == null) {
                throw new CrmebException("用户不存在");
            }

            // 构建HaiPay请求参数
            HaiPayRequestVo request = new HaiPayRequestVo();
            request.setAppId(appId);
            request.setOrderId(paymentRequest.getOrderNo());
            request.setName(paymentRequest.getNickname());
            request.setPhone(paymentRequest.getPhone());
            request.setEmail("<EMAIL>");
            request.setAmount(paymentRequest.getAmount().toString());
            request.setPayType(paymentRequest.getPayType());
            request.setInBankCode(inBankCode);
            request.setCallBackUrl(callBackUrl);
            request.setPartnerUserId("" + user.getUid());

            // 调用HaiPay服务
            HaiPayResponseVo response = haiPayService.createPayment(request);

            if (response == null || !PayConstants.API_STATUS_SUCCESS.equals(response.getStatus())) {
                logger.error("HaiPay支付创建失败，响应：{}", response);
                throw new CrmebException("支付创建失败：" + (response != null ? response.getMsg() : "未知错误"));
            }

            // 构建返回结果
            OrderPayResultResponse result = new OrderPayResultResponse();
            result.setOrderNo(response.getData().getOrderId());
            result.setAmount(paymentRequest.getAmount());
            result.setStatus(true);
            result.setPayType(request.getPayType());
            result.setPayChannel(PayConstants.PAY_TYPE_HAIPAY);
            result.setPayUrl(response.getData().getPayUrl());
            result.setQrCode(response.getData().getQrCode());
            result.setOutTradeNo(response.getData().getOrderNo());

            log.info("HaiPay支付创建成功，订单号：{}，支付URL：{}", paymentRequest.getOrderNo(), response.getData().getPayUrl());
            return result;

        } catch (Exception e) {
            log.error("HaiPay支付处理异常，订单号：{}", paymentRequest.getOrderNo(), e);
            throw new CrmebException("支付处理失败：" + e.getMessage());
        }
    }

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        log.info("HaiPay查询支付结果开始，订单号：{}", orderNo);

        try {
            // 获取HaiPay配置
            Long appId = Long.valueOf(systemConfigService.getValueByKey("haipay_app_id"));

            if (appId == 0) {
                throw new CrmebException("HaiPay配置不完整，请联系管理员");
            }

            // 构建HaiPay查询请求参数
            HaiPayQueryRequestVo request = new HaiPayQueryRequestVo();
            request.setAppId(appId);
            request.setOrderId(orderNo);

            // 调用HaiPay查询服务
            HaiPayQueryResponseVo response = haiPayService.queryPayment(request);

            if (response == null || response.getData() == null) {
                log.error("HaiPay查询失败，响应：{}", response);
                throw new CrmebException("查询失败：" + (response != null ? response.getMsg() : "未知错误"));
            }

            // 构建返回结果
            PaymentQueryResultResponse result = new PaymentQueryResultResponse();
            result.setOrderNo(response.getData().getOrderId());
            result.setOutTradeNo(response.getData().getOrderNo());
            result.setPayChannel(PayConstants.PAY_TYPE_HAIPAY);
            result.setAmount(new BigDecimal(response.getData().getAmount()));
            result.setTransactionId(response.getData().getTransactionId());
            result.setPayTime(response.getData().getPayTime());

            // 设置支付状态
            if (PayConstants.PAY_STATUS_SUCCESS.equals(response.getData().getStatus())) {
                result.setPaid(true);
                result.setActualAmount(new BigDecimal(response.getData().getActualAmount()));
                result.setFee(new BigDecimal(response.getData().getFee()));
            } else {
                result.setPaid(false);
                result.setActualAmount(BigDecimal.ZERO);
                result.setFee(BigDecimal.ZERO);
            }

            // 根据订单号前缀判断业务类型
            if (orderNo.startsWith("wxNo") || orderNo.startsWith("order")) {
                result.setBizType(PayConstants.BIZ_TYPE_ORDER);
            } else if (orderNo.startsWith("recharge")) {
                result.setBizType(PayConstants.BIZ_TYPE_RECHARGE);
            } else {
                result.setBizType("unknown");
            }

            log.info("HaiPay查询支付结果成功，订单号：{}，支付状态：{}", orderNo, result.getPaid());
            return result;

        } catch (Exception e) {
            log.error("HaiPay查询支付结果异常，订单号：{}", orderNo, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }

    @Override
    public String getPayChannel() {
        return PayConstants.PAY_TYPE_HAIPAY;
    }
} 