# 支付模块重构 - 策略模式实现

## 概述

本项目对支付模块进行了重构，使用策略模式来支持多种支付方式，并且兼容订单支付和充值支付两种业务场景。

## 支持的支付方式

- **微信支付** (`weixin`) - 支持公众号、小程序、H5支付
- **余额支付** (`yue`) - 用户余额支付
- **支付宝** (`alipay`) - 支付宝支付（待实现）
- **线下支付** (`offline`) - 线下支付（暂不支持）
- **Xendit支付** (`xendit`) - Xendit支付（待实现）
- **HaiPay支付** (`haipay`) - HaiPay支付（已实现）

## 支持的业务类型

- **订单支付** (`order`) - 商品订单支付
- **充值支付** (`recharge`) - 用户充值支付

## 架构设计

### 核心组件

1. **PaymentStrategy** - 支付策略接口
2. **PaymentStrategyFactory** - 支付策略工厂
3. **PaymentRequestVo** - 通用支付请求对象
4. **OrderPayResultResponse** - 支付结果响应对象

### 策略实现类

- `WeChatPaymentStrategy` - 微信支付策略
- `BalancePaymentStrategy` - 余额支付策略
- `AlipayPaymentStrategy` - 支付宝支付策略
- `OfflinePaymentStrategy` - 线下支付策略
- `XenditPaymentStrategy` - Xendit支付策略
- `HaiPayPaymentStrategy` - HaiPay支付策略

## 使用方式

### 订单支付

```java
// 构建支付请求
PaymentRequestVo paymentRequest = new PaymentRequestVo();
paymentRequest.setOrderNo("order123456");
paymentRequest.setAmount(new BigDecimal("100.00"));
paymentRequest.setUid(1);
paymentRequest.setPhone("13800138000");
paymentRequest.setNickname("测试用户");
paymentRequest.setPayType("haipay");
paymentRequest.setBizType("order");
paymentRequest.setIp("127.0.0.1");
paymentRequest.setFrom("h5");

// 获取支付策略并处理
PaymentStrategy strategy = paymentStrategyFactory.getStrategy("haipay");
OrderPayResultResponse response = strategy.processPayment(paymentRequest);
```

### 充值支付

```java
// 构建充值支付请求
PaymentRequestVo paymentRequest = new PaymentRequestVo();
paymentRequest.setOrderNo("recharge123456");
paymentRequest.setAmount(new BigDecimal("50.00"));
paymentRequest.setUid(1);
paymentRequest.setPhone("13800138000");
paymentRequest.setNickname("测试用户");
paymentRequest.setPayType("haipay");
paymentRequest.setBizType("recharge");
paymentRequest.setIp("127.0.0.1");
paymentRequest.setFrom("h5");
paymentRequest.setRechargeType("agent");

// 获取支付策略并处理
PaymentStrategy strategy = paymentStrategyFactory.getStrategy("haipay");
OrderPayResultResponse response = strategy.processPayment(paymentRequest);
```

## 支付回调处理

### 微信支付回调

支持订单支付和充值支付的回调处理，通过 `attach` 字段区分业务类型：

- `order` - 订单支付
- `recharge` - 充值支付

### HaiPay支付回调

支持订单支付和充值支付的回调处理，通过订单号前缀区分业务类型：

- `wxNo` 或 `order` 开头 - 订单支付
- `recharge` 开头 - 充值支付

## 任务处理

### 订单支付成功任务

- 任务常量：`ORDER_TASK_PAY_SUCCESS_AFTER`
- 任务类：`OrderPaySuccessTask`
- 处理逻辑：积分、经验、佣金、拼团等后置处理

### 充值支付成功任务

- 任务常量：`RECHARGE_TASK_PAY_SUCCESS_AFTER`
- 任务类：`RechargePaySuccessTask`
- 处理逻辑：余额增加、账单记录等后置处理

## 配置说明

### HaiPay配置

```properties
# HaiPay商户配置
haipay_merchant_id=your_merchant_id
haipay_merchant_key=your_merchant_key
haipay_api_url=https://api.haipay.com
haipay_in_bank_code=your_bank_code
haipay_notify_url=https://your-domain.com/api/admin/payment/callback/haipay
haipay_return_url=https://your-domain.com/payment/result
```

### 微信支付配置

```properties
# 公众号支付配置
pay_wechat_app_id=your_app_id
pay_wechat_mch_id=your_mch_id
pay_wechat_app_key=your_app_key

# 小程序支付配置
pay_routine_app_id=your_mini_app_id
pay_routine_mch_id=your_mch_id
pay_routine_app_key=your_app_key
```

## 扩展新的支付方式

1. 实现 `PaymentStrategy` 接口
2. 在 `PaymentStrategyFactory` 中注册新策略
3. 添加相应的配置参数
4. 实现回调处理逻辑（如需要）

### 示例：添加新的支付方式

```java
@Service
public class NewPaymentStrategy implements PaymentStrategy {
    
    @Override
    public OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest) {
        // 实现支付逻辑
        return response;
    }
    
    @Override
    public String getPayType() {
        return "new_payment";
    }
}
```

## 测试

运行测试类 `PaymentStrategyTest` 来验证支付策略的功能：

```bash
mvn test -Dtest=PaymentStrategyTest
```

## 注意事项

1. 所有支付策略都支持订单支付和充值支付两种业务类型
2. 支付回调需要根据业务类型进行不同的处理
3. 余额支付会直接处理支付逻辑，其他支付方式需要等待回调
4. 新增支付方式时需要同时支持订单和充值两种场景

## 版本历史

- v1.0.0 - 初始版本，支持微信、余额、支付宝、线下支付
- v1.1.0 - 添加Xendit支付支持
- v1.2.0 - 添加HaiPay支付支持
- v2.0.0 - 重构为策略模式，支持订单支付和充值支付 