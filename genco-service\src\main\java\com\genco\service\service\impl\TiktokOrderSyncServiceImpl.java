package com.genco.service.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.model.order.StoreOrderInfo;
import com.genco.common.model.user.User;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.OrderUtil;
import com.genco.service.service.*;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class TiktokOrderSyncServiceImpl implements TiktokOrderSyncService, PlatformOrderSyncService {
    private static final Logger logger = LoggerFactory.getLogger(TiktokOrderSyncServiceImpl.class);

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreOrderInfoService storeOrderInfoService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserRewardTaskService userRewardTaskService;


    @Override
    public String syncTiktokOrders(String pageToken, Date startTime, Date endTime) {
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_KEY));
        apiClient.setSecret(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_APP_SECRET));
        apiClient.setTokens(systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ACCESS_TOKEN));
        AffiliateCreatorV202410Api affiliateCreatorV202410Api = new AffiliateCreatorV202410Api(apiClient);
        SearchCreatorAffiliateOrdersResponse resp = null;
        String nextPageToken = null;
        try {
            SearchCreatorAffiliateOrdersRequestBody requestBody = new SearchCreatorAffiliateOrdersRequestBody();
            if (startTime != null && endTime != null) {
                requestBody.setCreateTimeGe(startTime.getTime() / 1000);
                requestBody.setCreateTimeLt(endTime.getTime() / 1000);
            }
            resp = affiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(
                    20L, apiClient.getTokens(), "application/json", pageToken, requestBody);
            if (resp != null && resp.getCode() != null && resp.getCode() == 0) {
                SearchCreatorAffiliateOrdersResponseData respData = resp.getData();
                nextPageToken = respData.getNextPageToken();
                Boolean execute = transactionTemplate.execute(e -> {
                    List<SearchCreatorAffiliateOrdersResponseDataOrders> orders = respData.getOrders();
                    if (orders == null) {
                        throw new CrmebException("订单同步结果查询异常");
                    }
                    StoreOrder storeOrder = new StoreOrder();
                    for (SearchCreatorAffiliateOrdersResponseDataOrders order : orders) {
                        List<StoreOrderInfo> orderInfos = CollUtil.newArrayList();
                        StoreOrder oldOrder = storeOrderService.getByOderId(order.getId());
                        Integer status = OrderUtil.orderStatusMapping(order.getStatus());
                        if (oldOrder != null) {
                            //状态没有变，或者状态已经是完结状态
                            if (oldOrder.getStatus().equals(status) || oldOrder.getStatus() == 6) {
                                continue;
                            }
                            storeOrder.setOrderId(order.getId());
                            storeOrder.setStatus(status);
                            storeOrder.setUpdateTime(new Date());
                            storeOrder.setId(oldOrder.getId());
                            storeOrderService.updateById(storeOrder);
                            nextByOrderStatus(oldOrder, status);
                            continue;
                        }
                        storeOrder.setCreateTime(DateUtil.timeStamp11ToDate(order.getCreateTime()));
                        storeOrder.setOutTradeNo(order.getId());
                        if (order.getDeliveryTime() != null) {
                            storeOrder.setDeliveryTime(DateUtil.timeStamp11ToDate(order.getDeliveryTime()));
                        }
                        storeOrder.setStatus(status);
                        storeOrder.setType(2);
                        storeOrder.setOrderId(order.getId());
                        storeOrder.setCreateTime(DateUtil.timeStamp11ToDate(order.getCreateTime()));
                        List<SearchCreatorAffiliateOrdersResponseDataOrdersSkus> skus = order.getSkus();
                        if (skus == null || CollUtil.isEmpty(skus)) {
                            throw new CrmebException("订单同步-order中不存在sku..");
                        }
                        BigDecimal totalPrice = new BigDecimal(0);
                        for (SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku : skus) {
                            orderInfos.add(generateStoreOrderInfo(order.getId(), sku, totalPrice));
                        }
                        storeOrder.setUid(OrderUtil.extraUidFromTagStr(skus.get(0).getTag()));
                        storeOrder.setTotalPrice(totalPrice);
                        User user = userService.getById(storeOrder.getUid());
                        if (user == null) {
                            logger.error("订单同步异常，用户不存在：uid=" + storeOrder.getUid() + "orderNo: " + storeOrder.getOutTradeNo());
                            continue;
                        }
                        storeOrder.setUserPhone(user.getPhone());
                        storeOrder.setRealName(user.getRealName());
                        storeOrder.setUserAddress("-");
                        storeOrder.setPayType("0");
                        storeOrder.setMark("-");
                        storeOrder.setCost(new BigDecimal("0"));
                        storeOrderService.create(storeOrder);
                        orderInfos.forEach(info -> info.setOrderId(storeOrder.getId()));
                        storeOrderInfoService.saveBatch(orderInfos);
                    }
                    return Boolean.TRUE;
                });
            }
        } catch (Exception e) {
            logger.error("TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: {}", e.getMessage(), e);
            throw new CrmebException("TiktokOrderSyncServiceImpl.syncTiktokOrders 拉取异常: " + e.getMessage());
        }
        return nextPageToken;
    }

    @Override
    public String getPlatform() {
        return "tiktok";
    }

    @Override
    public String syncOrders(String pageToken, Date startTime, Date endTime) {
        return syncTiktokOrders(pageToken, startTime, endTime);
    }

    private StoreOrderInfo generateStoreOrderInfo(String orderNo, SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku, BigDecimal totalPrice) {
        StoreOrderInfo storeOrderInfo = new StoreOrderInfo();
        storeOrderInfo.setInfo("-");
        storeOrderInfo.setUnique(orderNo);
        storeOrderInfo.setSku("-");
        storeOrderInfo.setImage("-");
        storeOrderInfo.setWeight(new BigDecimal(0));
        storeOrderInfo.setVolume(new BigDecimal(0));
        storeOrderInfo.setGiveIntegral(0);
        storeOrderInfo.setVipPrice(new BigDecimal(0));
        storeOrderInfo.setOrderNo(orderNo);
        storeOrderInfo.setTag(sku.getTag());
        storeOrderInfo.setContentId(sku.getContentId());
        storeOrderInfo.setPayNum(sku.getQuantity());
        storeOrderInfo.setOutProductId(sku.getProductId());
        storeOrderInfo.setCampaignId(sku.getCampaignId());
        storeOrderInfo.setProductName(sku.getProductName());
        storeOrderInfo.setProductType(5);
        storeOrderInfo.setShopName(sku.getShopName());
        storeOrderInfo.setRefundedQuantity(sku.getRefundedQuantity());
        storeOrderInfo.setReturnedQuantity(sku.getReturnedQuantity());
        if (sku.getPrice() != null && StringUtils.isNotEmpty(sku.getPrice().getAmount())) {
            storeOrderInfo.setPrice(new BigDecimal(StringUtils.substring(sku.getPrice().getAmount(), 2)));
            totalPrice = totalPrice.add(storeOrderInfo.getPrice());
        }
        storeOrderInfo.setPayNum(sku.getQuantity());
        if (sku.getActualBonusCommission() != null && StringUtils.isNotEmpty(sku.getActualBonusCommission().getAmount())) {
            storeOrderInfo.setActualBonusCommission(new BigDecimal(StringUtils.substring(sku.getActualBonusCommission().getAmount(), 2)));
        }
        if (sku.getActualCommission() != null && StringUtils.isNotEmpty(sku.getActualCommission().getAmount())) {
            storeOrderInfo.setActualCommission(new BigDecimal(StringUtils.substring(sku.getActualCommission().getAmount(), 2)));
        }
        if (sku.getActualCommissionBase() != null && StringUtils.isNotEmpty(sku.getActualCommissionBase().getAmount())) {
            storeOrderInfo.setActualCommissionBase(new BigDecimal(StringUtils.substring(sku.getActualCommissionBase().getAmount(), 2)));
        }
        if (sku.getActualCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getActualCreatorCommissionRewardFee().getAmount())) {
            storeOrderInfo.setActualCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getActualCreatorCommissionRewardFee().getAmount(), 2)));
        }
        if (sku.getCommissionBonusRate() != null) {
            storeOrderInfo.setCommissionBonusRate(new BigDecimal(sku.getCommissionBonusRate()));
        }
        if (sku.getEstimatedBonusCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedBonusCommission().getAmount())) {
            storeOrderInfo.setEstimatedBonusCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedBonusCommission().getAmount(), 2)));
        }
        if (sku.getEstimatedCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedCommission().getAmount())) {
            storeOrderInfo.setEstimatedCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedCommission().getAmount(), 2)));
        }
        if (sku.getEstimatedShopAdsCommission() != null && StringUtils.isNotEmpty(sku.getEstimatedShopAdsCommission().getAmount())) {
            storeOrderInfo.setEstimatedShopAdsCommission(new BigDecimal(StringUtils.substring(sku.getEstimatedShopAdsCommission().getAmount(), 2)));
        }
        if (sku.getEstimatedCommissionBase() != null && StringUtils.isNotEmpty(sku.getEstimatedCommissionBase().getAmount())) {
            storeOrderInfo.setEstimatedCommissionBase(new BigDecimal(StringUtils.substring(sku.getEstimatedCommissionBase().getAmount(), 2)));
        }
        if (sku.getEstimatedCreatorCommissionRewardFee() != null && StringUtils.isNotEmpty(sku.getEstimatedCreatorCommissionRewardFee().getAmount())) {
            storeOrderInfo.setEstimatedCreatorCommissionRewardFee(new BigDecimal(StringUtils.substring(sku.getEstimatedCreatorCommissionRewardFee().getAmount(), 2)));
        }
        if (sku.getShopAdsCommissionRate() != null) {
            storeOrderInfo.setShopAdsCommissionRate(sku.getShopAdsCommissionRate());
        }
        return storeOrderInfo;
    }

    /**
     * 根据下一步状态，判断执行动作
     *
     * @param nextStatus The current status of the order. Possible options are:
     *                   - UNSPECIFIED: The status of the order is undefined. It might be updated later.
     *                   - ORDERED: The order has been placed, but the commission has not been settled. But an estimated commission is available.
     *                   - SETTLED: The commission of the order is already settled.
     *                   - REFUNDED: The order has been returned/refunded/canceled by the buyer, and no commission will be settled.
     *                   - FROZEN: Possible fraud has been detected regarding the order. The commission will be unfrozen after the fraud is resolved.
     *                   - DEDUCTED: Additional deduction from your balance account.
     * @param nextStatus
     * @return 返回状态值
     * <p>
     * 0：待发货；1：待收货；2：已收货，待评价；3：已完成
     * ——
     * 4：UNSPECIFIED
     * 5：ORDERED
     * 6：SETTLED
     * 7：REFUNDED
     * 8：FROZEN
     * 9：DEDUCTED
     */
    private void nextByOrderStatus(StoreOrder storeOrder, Integer nextStatus) {
        if (nextStatus == 4 || nextStatus == 5 || nextStatus == 8) {
            //状态未知，已下定进行中，冻结中

        } else if (nextStatus == 6) {
            //完成结算了，需要结算（落地记录）
            userRewardTaskService.createOrderRewardTask(storeOrder.getUid(), storeOrder.getOrderId(),
                    null, JSONUtil.toJsonStr(storeOrder));
        } else if (nextStatus == 7) {
            //退款了 无需结算

        } else if (nextStatus == 9) {
            //额外的账户处理 TODO（落地记录）
        }
    }
}