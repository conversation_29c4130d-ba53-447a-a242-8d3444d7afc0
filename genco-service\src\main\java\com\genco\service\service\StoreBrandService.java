package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.brand.StoreBrand;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreBrandUpdateRequest;

import java.util.List;

/**
 * StoreBrandService 接口
 */
public interface StoreBrandService extends IService<StoreBrand> {

    /**
     * 获取品牌列表
     *
     * @return 品牌列表
     */
    List<StoreBrand> getBrandList(Integer type, String keywords, PageParamRequest pageParamRequest);

    /**
     * 获取品牌详情
     *
     * @param code 品牌编码
     * @return StoreProductResponse
     */
    StoreBrand getByBrandCode(String code);

    /**
     * 更新品牌信息
     *
     * @param request 品牌更新请求对象
     * @return 是否更新成功
     */
    Boolean updateBrand(StoreBrandUpdateRequest request);

    /**
     * 新增品牌信息
     *
     * @param request 品牌新增请求对象
     * @return 新增品牌ID
     */
    Integer addBrand(StoreBrandUpdateRequest request);

    /**
     * 逻辑删除品牌
     * @param id 品牌ID
     * @return 是否删除成功
     */
    Boolean deleteBrand(Integer id);
}
