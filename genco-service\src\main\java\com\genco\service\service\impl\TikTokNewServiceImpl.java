package com.genco.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.genco.common.config.CrmebConfig;
import com.genco.common.constants.WeChatConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.token.TikTokOauthToken;
import com.genco.common.utils.RedisUtil;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.TikTokNewService;
import org.apache.http.NameValuePair;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.genco.common.constants.WeChatConstants.TIKTOK_USER_INFO_URL;


/**
 * 微信公用服务实现类
 */
@Service
public class TikTokNewServiceImpl implements TikTokNewService {
    private static final Logger logger = LoggerFactory.getLogger(TikTokNewServiceImpl.class);

    @Autowired
    private CrmebConfig crmebConfig;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 获取开放平台access_token
     * 通过 code 获取
     * 公众号使用
     *
     * @return 开放平台accessToken对象
     */
    @Override
    public TikTokOauthToken getOauth2AccessToken(String code, String codeVerifier, String platform) {
        // TODO 上线前需要替换成统一的
        String appId = "awnb2jrqphg8q3x9";
        String secret = "THUgLUinHl6ymTkeYa3RQqUEcmQVa4ox";
        String redirectUri;
        if ("ios".equalsIgnoreCase(platform)) {
            redirectUri = "https://genconusantara.com/auth/callback";
        } else if ("android".equalsIgnoreCase(platform)) {
            redirectUri = "https://genconusantara.com/callback";
        } else {
            throw new CrmebException("不支持的平台类型platform:" + platform);
        }
        if (StrUtil.isBlank(appId)) {
            throw new CrmebException("TikTok appId未设置");
        }
        if (StrUtil.isBlank(secret)) {
            throw new CrmebException("TikTok secret未设置");
        }
        TikTokOauthToken tikTokOauthToken = new TikTokOauthToken();

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(WeChatConstants.TIKTOK_OAUTH2_ACCESS_TOKEN_URL);
            // 设置请求头
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            httpPost.setHeader("Cache-Control", "no-cache");

            // 构建参数列表（手动控制编码）
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("client_key", appId));
            params.add(new BasicNameValuePair("client_secret", secret));
            params.add(new BasicNameValuePair("code", code));
            params.add(new BasicNameValuePair("code_verifier", codeVerifier));
            params.add(new BasicNameValuePair("grant_type", "authorization_code"));
            params.add(new BasicNameValuePair("redirect_uri", redirectUri));

            // 设置请求体
            String formData = params.stream()
                    .map(p -> p.getName() + "=" + p.getValue())
                    .collect(Collectors.joining("&"));

            httpPost.setEntity(new StringEntity(formData));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                String responseBody = EntityUtils.toString(response.getEntity());
                // 返回解析后的响应体
                if (ObjectUtil.isNull(responseBody)) {
                    throw new CrmebException("平台接口异常，没任何数据返回！");
                }
                tikTokOauthToken = JSONObject.parseObject(responseBody, TikTokOauthToken.class);
            }
        } catch (Exception e) {
            JSONObject error = new JSONObject();
            error.put("error", "request_failed");
            error.put("message", e.getMessage());

            tikTokOauthToken.setError(error);
        }
        return tikTokOauthToken;
    }

    @Override
    public String getTiktokAccount(String accessToken, String openId) {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 构建带查询参数的URL
            URIBuilder uriBuilder = new URIBuilder(TIKTOK_USER_INFO_URL);
            uriBuilder.addParameter("access_token", accessToken);
            uriBuilder.addParameter("open_id", openId);

            HttpGet httpGet = new HttpGet(uriBuilder.build());

            // 设置必要的请求头
            httpGet.setHeader("Accept", "application/json");
            httpGet.setHeader("Cache-Control", "no-cache");

            try (CloseableHttpResponse response = httpClient.execute(httpGet)) {
                int statusCode = response.getStatusLine().getStatusCode();

                if (statusCode == 200) {
                    String responseBody = EntityUtils.toString(response.getEntity());
                    JSONObject jsonResponse = JSONObject.parseObject(responseBody);

                    // 提取data对象中的用户信息
                    if (jsonResponse.containsKey("data")) {
                        JSONObject data = jsonResponse.getJSONObject("data");
                        // Get other info and create model,to get other info
                        logger.info(data.toString());
                        // 可以添加其他需要的字段
                        // userInfo.put("avatar_url", data.getString("avatar_url"));
                        return data.getString("display_name");
                    } else {
                        throw new Exception("TikTok API response missing 'data' field");
                    }
                } else {
                    throw new Exception("TikTok API returned HTTP " + statusCode);
                }
            }
        } catch (Exception e) {
            // 错误处理
            JSONObject error = new JSONObject();
            error.put("error", "request_failed");
            error.put("message", e.getMessage());
        }

        return "";
    }
}

