package com.genco.service.service.impl;

import com.genco.common.enums.ProductChannelEnum;
import com.genco.common.utils.RestTemplateUtil;
import com.genco.service.service.TikTokProductIdService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 商品渠道工具类
 * <p>
 * 用于识别商品URL所对应的电商平台（如TikTok、Shopee等），
 * 并支持从TikTok商品URL中提取产品ID。
 * 主要功能包括：
 * <ul>
 *   <li>根据商品详情页URL自动识别所属电商平台</li>
 *   <li>从TikTok商品URL中提取产品ID</li>
 * </ul>
 * 适用于需要对多平台商品链接进行统一处理和解析的场景。
 * <p>
 * 示例：
 * <pre>
 *   ProductChannelEnum platform = ProductChannelUtil.identifyPlatform(url);
 *   String productId = ProductChannelUtil.extractProductId(url);
 * </pre>
 */
@Service
public class TikTokProductIdServiceImpl implements TikTokProductIdService {

    @Resource
    private RestTemplateUtil restTemplateUtil;

    // 匹配TikTok商品ID的正则表达式
    private static final Pattern TIKTOK_PRODUCT_ID_PATTERN = Pattern.compile(
            "/(?:product|view)/?(\\d{10,})[/?]?",
            Pattern.CASE_INSENSITIVE
    );

    // 主正则表达式
    private static final Pattern PRIMARY_PATTERN = Pattern.compile(
            "tiktok\\.[a-z]{2,10}/(?:product/view|view/product|v)/(\\d{10,20})",
            Pattern.CASE_INSENSITIVE
    );

    // 备用正则表达式
    private static final Pattern FALLBACK_PATTERN = Pattern.compile(
            "/product/(\\d{10,20})(?:/|$|\\?)",
            Pattern.CASE_INSENSITIVE
    );

    // 预编译正则表达式
    private static final Pattern SHORT_LINK_PATTERN = Pattern.compile(
            "https?://(?:vt|vm|www)\\.(?:tiktok|tokopedia)\\.[a-z]{2,10}/(?:t/)?[a-zA-Z0-9_-]{8,32}(?:/|$)",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern PRODUCT_ID_PATTERN = Pattern.compile(
            "(?:product/view|view/product|v)/(\\d{10,20})",
            Pattern.CASE_INSENSITIVE
    );


    /**
     * 根据商品URL识别电商平台
     *
     * @param url 商品详情页URL
     * @return 平台枚举值
     */
    @Override
    public ProductChannelEnum identifyPlatform(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        // 统一转换为小写提高容错性
        String normalizedUrl = url.toLowerCase();

        if (normalizedUrl.contains("tiktok")) {
            return ProductChannelEnum.TIKTOK;
        } else if (normalizedUrl.contains("shopee")) {
            return ProductChannelEnum.SHOPEE;
        }
        return ProductChannelEnum.UNKNOWN;
    }

    /**
     * 从TikTok商品URL中提取产品ID
     * "https://shopee.com/Apple-iPhone-15-Pro-Max-i.123456789.1729714831816951615",
     * "http://m.shopee.co.id/product/987654321/1234567890123456789?referrer=share",
     * "www.tiktok.com/product/view/1729714831816951615",
     * "https://vm.tiktok.com/product/view/1234567890123456789/",
     * "https://shop.tiktok.com/view/product/1729714831816951615?region=ID&local=en",
     * "https://amazon.com/product/123",  // 未知平台
     * "invalid.url.com",                // 无效URL
     *
     * @param url 商品URL（支持完整URL或部分路径）
     * @return 产品ID字符串，未找到返回null
     */
    @Override
    public String extractProductId(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }

        // 1. 尝试主正则表达式
        String productId = extractWithPattern(url, PRIMARY_PATTERN);
        if (productId != null) return productId;

        // 2. 尝试备用正则表达式
        productId = extractWithPattern(url, FALLBACK_PATTERN);
        if (productId != null) return productId;

        // 3. 检查是否为短链接
        if (isValidTikTokShortLink(url)) {
            try {
                // 2. 发送 HEAD 请求获取重定向 URL
                String redirectedUrl = restTemplateUtil.getRedirectUrl(url);

                // 3. 检查重定向的地址是否为商品地址
                Matcher matcher = PRODUCT_ID_PATTERN.matcher(redirectedUrl);
                if (!matcher.find()) {
                    return redirectedUrl;
                }
                // 3. 从重定向 URL 提取商品 ID
                return extractProductIdFromUrl(redirectedUrl);
            } catch (Exception e) {
                // 记录日志或处理异常 TODO
                return null;
            }
        }
        return null;
    }


    private static String extractWithPattern(String url, Pattern pattern) {
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 验证短链接格式
     */
    public static boolean isValidTikTokShortLink(String url) {
        return url != null && SHORT_LINK_PATTERN.matcher(url).find();
    }

    /**
     * 从 URL 提取商品 ID
     */
    private static String extractProductIdFromUrl(String url) {
        if (url == null) return null;

        Matcher matcher = PRODUCT_ID_PATTERN.matcher(url);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
