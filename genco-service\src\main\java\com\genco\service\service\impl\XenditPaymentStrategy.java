package com.genco.service.service.impl;

import com.genco.common.constants.Constants;
import com.genco.common.constants.PayConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.finance.UserRecharge;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.utils.DateUtil;
import com.genco.common.vo.PaymentRequestVo;
import com.genco.service.service.PaymentStrategy;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.UserRechargeService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

import static java.lang.Boolean.TRUE;

/**
 * Xendit支付策略实现
 */
@Slf4j
@Service
public class XenditPaymentStrategy implements PaymentStrategy {

    private static final Logger logger = LoggerFactory.getLogger(XenditPaymentStrategy.class);

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private UserRechargeService userRechargeService;

    @Override
    public OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest) {
        logger.info("Xendit支付处理开始，订单号: {}, 金额: {}", paymentRequest.getOrderNo(), paymentRequest.getAmount());

        try {
            // TODO: 实现Xendit支付逻辑
            // 1. 获取Xendit配置
            String xenditApiKey = systemConfigService.getValueByKey("xendit_api_key");
            String xenditCallbackUrl = systemConfigService.getValueByKey("xendit_callback_url");

            if (xenditApiKey == null || xenditApiKey.isEmpty()) {
                throw new CrmebException("Xendit API密钥未配置");
            }

            // 2. 构建Xendit支付请求参数
            // 这里需要根据Xendit API文档构建具体的请求参数
            // 例如：
            // - external_id: 订单号
            // - amount: 支付金额
            // - currency: 货币类型
            // - payment_methods: 支付方式
            // - callback_url: 回调地址

            // 3. 调用Xendit API创建支付订单
            // String paymentUrl = xenditService.createPayment(paymentRequest);

            // 4. 返回支付结果
            OrderPayResultResponse response = new OrderPayResultResponse();
            response.setOrderNo(paymentRequest.getOrderNo());
            response.setPayType(paymentRequest.getPayType());
            response.setPayChannel(PayConstants.PAY_TYPE_XENDIT);
            response.setStatus(true);
            response.setAmount(paymentRequest.getAmount());
            response.setOutTradeNo(paymentRequest.getOrderNo());

            // TODO: 设置Xendit支付链接
            // response.setXenditPaymentUrl(paymentUrl);

            logger.info("Xendit支付处理完成，订单号: {}", paymentRequest.getOrderNo());
            return response;

        } catch (Exception e) {
            logger.error("Xendit支付处理失败，订单号: {}, 错误信息: {}", paymentRequest.getOrderNo(), e.getMessage());
            throw new CrmebException("Xendit支付处理失败: " + e.getMessage());
        }
    }

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        logger.info("Xendit支付查询支付结果开始，订单号：{}", orderNo);

        try {
            // 这里需要根据具体的Xendit API来实现

            // 根据订单号前缀判断业务类型
            String bizType = "unknown";
            if (orderNo.startsWith("wxNo") || orderNo.startsWith("order")) {
                bizType = PayConstants.BIZ_TYPE_ORDER;
            } else if (orderNo.startsWith("recharge")) {
                bizType = PayConstants.BIZ_TYPE_RECHARGE;

            }

            // 构建返回结果
            PaymentQueryResultResponse result = new PaymentQueryResultResponse();
            result.setOrderNo(orderNo);
            result.setOutTradeNo(orderNo);
            result.setPayChannel(PayConstants.PAY_TYPE_XENDIT);
            result.setBizType(bizType);
            result.setPaid(false);
            result.setAmount(BigDecimal.ZERO);

            //目前实现本地单据的查询，依赖于xendit回调更新状态
            if (bizType.equals(PayConstants.BIZ_TYPE_RECHARGE)) {
                UserRecharge userRecharge = userRechargeService.getInfoByEntity(
                        new UserRecharge().setOrderId(orderNo));
                result.setPaid(userRecharge.getPaid());
                result.setAmount(userRecharge.getPrice());
                result.setOutTradeNo(userRecharge.getOutTradeNo());
                if (userRecharge.getPaid() != null && userRecharge.getPayTime() != null) {
                    result.setPayTime(DateUtil.dateToStr(userRecharge.getPayTime(), Constants.DATE_FORMAT));
                }
                //如果付款成功
                if (userRecharge.getPaid() == TRUE) {
                    result.setActualAmount(userRecharge.getPrice());
                }
            }

            logger.info("Xendit支付查询支付结果成功，订单号：{}，支付状态：{}", orderNo, result.getPaid());
            return result;

        } catch (Exception e) {
            logger.error("Xendit支付查询支付结果异常，订单号：{}", orderNo, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }

    @Override
    public String getPayChannel() {
        return PayConstants.PAY_TYPE_XENDIT;
    }
} 