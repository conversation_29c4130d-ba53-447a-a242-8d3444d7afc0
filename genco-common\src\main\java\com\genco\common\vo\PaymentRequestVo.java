package com.genco.common.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 通用支付请求VO
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "PaymentRequestVo对象", description = "通用支付请求")
public class PaymentRequestVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "用户ID")
    private Integer uid;

    @ApiModelProperty(value = "用户手机号")
    private String phone;

    @ApiModelProperty(value = "用户昵称")
    private String nickname;

    @ApiModelProperty(value = "支付类型")
    private String payType;

    @ApiModelProperty(value = "业务类型：order-订单支付，recharge-充值支付")
    private String bizType;

    @ApiModelProperty(value = "IP地址")
    private String ip;

    @ApiModelProperty(value = "支付来源：app，h5")
    private String from;

    @ApiModelProperty(value = "充值类型（仅充值时使用）")
    private String rechargeType;
} 