<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!-- 属性配置 -->
    <property name="APP_NAME" value="Genco"/>
    <property name="log.path" value="./genco_log"/>

    <!-- 摘要日志输出器 -->
    <appender name="DIGEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/log_digest.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                {
                "app": "${APP_NAME}",
                "timestamp":"%d{yyyy-MM-dd HH:mm:ss.SSS}",
                "level": "%level",
                "thread": "%thread",
                "class": "%logger{40}",
                "type": "DIGEST",
                "message": "%msg" }
                %n
            </pattern>
            <charset>utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${APP_NAME}-digest-log-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <!-- 只记录包含[DIGEST]的日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <expression>return message.contains("[DIGEST]");</expression>
            </evaluator>
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>DENY</OnMismatch>
        </filter>
    </appender>

    <!-- 详细日志输出器 -->
    <appender name="DETAIL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/log_detail.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                {
                "app": "${APP_NAME}",
                "timestamp":"%d{yyyy-MM-dd HH:mm:ss.SSS}",
                "level": "%level",
                "thread": "%thread",
                "class": "%logger{40}",
                "type": "DETAIL",
                "message": "%msg" }
                %n
            </pattern>
            <charset>utf-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/${APP_NAME}-log-detail-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <!-- 只记录包含[DETAIL]的日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <expression>return message.contains("[DETAIL]");</expression>
            </evaluator>
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>DENY</OnMismatch>
        </filter>
    </appender>

    <!-- 控制台输出器（包含摘要和详细日志） -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>
                {
                "app": "${APP_NAME}",
                "timestamp":"%d{yyyy-MM-dd HH:mm:ss.SSS}",
                "level": "%level",
                "thread": "%thread",
                "class": "%logger{40}",
                "message": "%msg" }
                %n
            </pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- LoggerUtil专用Logger配置 -->
    <logger name="com.genco.common.utils.LoggerUtil" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="DIGEST_FILE"/>
        <appender-ref ref="DETAIL_FILE"/>
    </logger>

    <!-- 根Logger配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration> 