# 外部支付单号和支付渠道功能实现

## 概述

为了支持多种支付平台，我们在用户充值表中新增了 `out_trade_no` 字段来存储外部支付平台返回的订单号，以及 `pay_channel` 字段来存储支付渠道信息。这样可以在支付回调时根据外部支付单号来查询对应的充值订单，并且可以按支付渠道进行统计和查询。

## 功能特性

1. **数据库字段扩展**：在 `eb_user_recharge` 表中新增 `out_trade_no` 和 `pay_channel` 字段
2. **支付策略支持**：所有支付策略都会返回外部支付单号和支付渠道
3. **回调处理增强**：支付回调支持根据外部支付单号查询充值订单，并记录支付渠道
4. **查询接口**：提供根据外部支付单号和支付渠道查询充值订单的服务方法
5. **统计分析**：支持按支付渠道进行充值统计

## 数据库变更

### 新增字段

```sql
-- 给 eb_user_recharge 表添加外部支付单号字段
ALTER TABLE `eb_user_recharge` 
ADD COLUMN `out_trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部支付单号' AFTER `refund_price`;

-- 给 eb_user_recharge 表添加支付渠道字段
ALTER TABLE `eb_user_recharge` 
ADD COLUMN `pay_channel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付渠道' AFTER `out_trade_no`;

-- 添加索引以提高查询性能
ALTER TABLE `eb_user_recharge` 
ADD INDEX `idx_out_trade_no` (`out_trade_no`) USING BTREE;

-- 添加支付渠道索引
ALTER TABLE `eb_user_recharge` 
ADD INDEX `idx_pay_channel` (`pay_channel`) USING BTREE;
```

## 代码变更

### 1. 实体类更新

- `UserRecharge.java` - 新增 `outTradeNo` 和 `payChannel` 字段
- `UserRechargeResponse.java` - 新增 `outTradeNo` 和 `payChannel` 字段
- `OrderPayResultResponse.java` - 新增 `outTradeNo` 和 `payChannel` 字段
- `PayCallbackRequest.java` - 新增 `payChannel` 字段

### 2. 服务层更新

- `UserRechargeService.java` - 新增 `getByOutTradeNo` 和 `getListByPayChannel` 方法
- `UserRechargeServiceImpl.java` - 实现相关查询方法
- `OrderPayServiceImpl.java` - 在支付成功后更新外部支付单号和支付渠道

### 3. 支付策略更新

所有支付策略都会在返回结果中设置外部支付单号和支付渠道：

- `HaiPayPaymentStrategy` - 设置 HaiPay 返回的 `orderNo` 和 `haipay` 渠道
- `WeChatPaymentStrategy` - 设置微信支付返回的 `out_trade_no` 和 `weixin` 渠道
- `BalancePaymentStrategy` - 设置内部订单号和 `yue` 渠道
- `XenditPaymentStrategy` - 设置内部订单号和 `xendit` 渠道

### 4. 回调处理更新

- `CallbackServiceImpl.java` - 支持根据外部支付单号查询充值订单，并记录支付渠道
- `CallbackController.java` - 在通用回调中传递外部支付单号和支付渠道

## 使用方式

### 1. 支付下单

```java
// 构建支付请求
PaymentRequestVo paymentRequest = new PaymentRequestVo();
paymentRequest.setOrderNo("recharge123456");
paymentRequest.setAmount(new BigDecimal("100.00"));
paymentRequest.setUid(1);
paymentRequest.setPayType("haipay");
paymentRequest.setBizType("recharge");

// 获取支付策略并处理
PaymentStrategy strategy = paymentStrategyFactory.getStrategy("haipay");
OrderPayResultResponse response = strategy.processPayment(paymentRequest);

// 外部支付单号和支付渠道会自动更新到充值订单中
String outTradeNo = response.getOutTradeNo();
String payChannel = response.getPayChannel();
```

### 2. 支付回调

```java
// 支付回调时会自动根据外部支付单号查询充值订单
// 支持两种查询方式：
// 1. 根据内部订单号查询
UserRecharge recharge1 = userRechargeService.getInfoByEntity(
    new UserRecharge().setOrderId("recharge123456")
);

// 2. 根据外部支付单号查询
UserRecharge recharge2 = userRechargeService.getByOutTradeNo("haipay_order_789");
```

### 3. 查询充值订单

```java
// 根据外部支付单号查询充值订单
UserRecharge recharge = userRechargeService.getByOutTradeNo("external_order_id");

if (recharge != null) {
    System.out.println("充值订单号: " + recharge.getOrderId());
    System.out.println("外部支付单号: " + recharge.getOutTradeNo());
    System.out.println("支付渠道: " + recharge.getPayChannel());
    System.out.println("充值金额: " + recharge.getPrice());
    System.out.println("支付状态: " + recharge.getPaid());
}

// 根据支付渠道查询充值订单列表
List<UserRecharge> haipayRecharges = userRechargeService.getListByPayChannel("haipay");
List<UserRecharge> wechatRecharges = userRechargeService.getListByPayChannel("weixin");
```

## 支持的支付平台

### 1. HaiPay

- 外部支付单号：HaiPay 返回的 `orderNo`
- 支付渠道：`haipay`
- 回调处理：支持根据 HaiPay 订单号查询充值订单

### 2. 微信支付

- 外部支付单号：微信支付返回的 `transaction_id`
- 支付渠道：`weixin`
- 回调处理：支持根据微信交易号查询充值订单

### 3. 余额支付

- 外部支付单号：内部订单号
- 支付渠道：`yue`
- 回调处理：直接使用内部订单号

### 4. Xendit

- 外部支付单号：内部订单号（待实现具体逻辑）
- 支付渠道：`xendit`
- 回调处理：待实现

## 注意事项

1. **字段长度**：`out_trade_no` 字段长度为 64 字符，`pay_channel` 字段长度为 32 字符
2. **索引优化**：为两个字段都添加了索引，提高查询性能
3. **兼容性**：新功能向后兼容，不会影响现有代码
4. **数据一致性**：在支付成功和回调处理时都会更新外部支付单号和支付渠道
5. **支付渠道常量**：使用 `PayConstants` 中定义的常量来确保一致性

## 测试建议

1. **支付下单测试**：验证外部支付单号和支付渠道是否正确设置
2. **回调处理测试**：验证是否能根据外部支付单号正确查询充值订单
3. **查询接口测试**：验证 `getByOutTradeNo` 和 `getListByPayChannel` 方法是否正常工作
4. **数据一致性测试**：验证外部支付单号和支付渠道在各个环节是否正确传递和保存
5. **统计分析测试**：验证按支付渠道统计功能是否正常

## 部署步骤

1. 执行数据库迁移脚本 `sql/add_out_trade_no_to_user_recharge.sql`
2. 部署更新后的代码
3. 测试支付功能是否正常工作
4. 验证外部支付单号和支付渠道是否正确保存和查询
5. 测试按支付渠道的统计功能 