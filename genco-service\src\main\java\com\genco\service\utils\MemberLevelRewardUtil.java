package com.genco.service.utils;

import com.genco.common.constants.Constants;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.model.user.User;
import com.genco.service.service.SystemUserLevelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员等级邀请奖励工具类
 * 用于处理基于会员等级的邀请奖励配置和计算
 */
@Component
public class MemberLevelRewardUtil {

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    // 缓存会员等级信息
    private Map<Integer, SystemUserLevel> levelCache = new HashMap<>();

    /**
     * 获取用户的会员等级grade
     * @param user 用户对象
     * @return 会员等级grade (0-4)，如果未找到返回0
     */
    public Integer getUserMemberGrade(User user) {
        if (user == null || user.getLevel() == null) {
            return 0; // 默认为0级普通用户
        }
        
        SystemUserLevel userLevel = getUserLevel(user.getLevel());
        if (userLevel == null) {
            return 0; // 如果等级配置不存在，默认为0级
        }
        
        return userLevel.getGrade() != null ? userLevel.getGrade() : 0;
    }

    /**
     * 获取用户等级配置信息
     * @param levelId 等级ID
     * @return SystemUserLevel对象
     */
    public SystemUserLevel getUserLevel(Integer levelId) {
        if (levelId == null) {
            return null;
        }
        
        // 先从缓存获取
        if (levelCache.containsKey(levelId)) {
            return levelCache.get(levelId);
        }
        
        // 从数据库获取并缓存
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(levelId);
        if (userLevel != null) {
            levelCache.put(levelId, userLevel);
        }
        
        return userLevel;
    }

    /**
     * 获取一级邀请奖励配置key
     * @param inviterGrade 邀请人等级 (0-4)
     * @param inviteeGrade 被邀请人等级 (0-4)
     * @return 配置key
     */
    public String getFirstLevelRewardKey(Integer inviterGrade, Integer inviteeGrade) {
        return String.format("member_level_%d_invite_level_%d_reward_1l", 
                inviterGrade != null ? inviterGrade : 0, 
                inviteeGrade != null ? inviteeGrade : 0);
    }

    /**
     * 获取二级邀请奖励配置key
     * @param inviterGrade 邀请人等级 (0-4)
     * @return 配置key
     */
    public String getSecondLevelRewardKey(Integer inviterGrade) {
        return String.format("member_level_%d_invite_reward_2l", 
                inviterGrade != null ? inviterGrade : 0);
    }

    /**
     * 获取三级邀请奖励配置key
     * @param inviterGrade 邀请人等级 (0-4)
     * @return 配置key
     */
    public String getThirdLevelRewardKey(Integer inviterGrade) {
        return String.format("member_level_%d_invite_reward_3l", 
                inviterGrade != null ? inviterGrade : 0);
    }

    /**
     * 验证会员等级是否有效
     * @param grade 会员等级
     * @return true-有效，false-无效
     */
    public boolean isValidMemberGrade(Integer grade) {
        return grade != null && grade >= 0 && grade <= 4;
    }

    /**
     * 获取会员等级名称
     * @param grade 会员等级
     * @return 等级名称
     */
    public String getMemberGradeName(Integer grade) {
        if (!isValidMemberGrade(grade)) {
            return "未知等级";
        }
        
        switch (grade) {
            case 0: return "普通用户";
            case 1: return "铜牌会员";
            case 2: return "银牌会员";
            case 3: return "金牌会员";
            case 4: return "钻石会员";
            default: return "未知等级";
        }
    }

    /**
     * 清除等级缓存
     */
    public void clearLevelCache() {
        levelCache.clear();
    }

    /**
     * 刷新指定等级的缓存
     * @param levelId 等级ID
     */
    public void refreshLevelCache(Integer levelId) {
        if (levelId != null) {
            levelCache.remove(levelId);
            getUserLevel(levelId); // 重新加载到缓存
        }
    }

    /**
     * 获取所有可用的会员等级列表
     * @return 会员等级列表
     */
    public List<SystemUserLevel> getAllUsableLevels() {
        return systemUserLevelService.getUsableList();
    }

    /**
     * 根据经验值获取应该达到的会员等级
     * @param experience 经验值
     * @return 会员等级配置
     */
    public SystemUserLevel getLevelByExperience(Integer experience) {
        if (experience == null || experience < 0) {
            return null;
        }
        
        List<SystemUserLevel> levels = getAllUsableLevels();
        SystemUserLevel targetLevel = null;
        
        for (SystemUserLevel level : levels) {
            if (experience >= level.getExperience()) {
                targetLevel = level;
            } else {
                break;
            }
        }
        
        return targetLevel;
    }
}
