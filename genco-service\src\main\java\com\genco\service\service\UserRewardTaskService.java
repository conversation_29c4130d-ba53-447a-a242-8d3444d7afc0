package com.genco.service.service;

import com.genco.service.model.UserRewardTask;

import java.util.List;

/**
 * 用户奖励/返佣任务服务接口。
 * 支持任务的写入、查询、便捷创建等。
 */
public interface UserRewardTaskService {
    /**
     * 新增任务
     */
    void addTask(UserRewardTask task);

    /**
     * 更新任务
     */
    void updateTask(UserRewardTask task);

    /**
     * 根据ID获取任务
     */
    UserRewardTask getTaskById(Long id);

    /**
     * 获取待处理任务
     */
    List<UserRewardTask> getPendingTasks(int limit);

    /**
     * 创建订单返佣任务
     *
     * @param userId      购物人ID
     * @param orderId     订单号
     * @param spreadUid   上级ID
     * @param contextJson 任务上下文
     */
    void createOrderRewardTask(Integer userId, String orderId, Integer spreadUid, String contextJson);

    /**
     * 创建邀请奖励任务
     *
     * @param userId      被邀请人ID
     * @param inviteUid   邀请人ID
     * @param contextJson 任务上下文
     */
    void createInviteRewardTask(Integer userId, Integer inviteUid, String contextJson);

    /**
     * 创建代理奖励任务
     *
     * @param userId      代理会员ID
     * @param inviteUid   邀请人ID
     * @param contextJson 任务上下文
     */
    void createAgentRewardTask(Integer userId, Integer inviteUid, String contextJson);
} 