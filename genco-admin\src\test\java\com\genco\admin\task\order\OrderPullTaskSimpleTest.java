package com.genco.admin.task.order;

import com.genco.service.model.tiktok.OrderPullTask;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 订单拉取任务简化测试类
 * 避免Spring版本兼容性问题，专注于核心逻辑测试
 */
public class OrderPullTaskSimpleTest {

    @Test
    public void testOrderPullTaskModel() {
        // 测试实体类基本功能
        OrderPullTask task = new OrderPullTask();
        
        // 设置基本属性
        task.setPlatform("tiktok");
        task.setBatchNo("test_batch_001");
        task.setPageNo(1);
        task.setStatus(OrderPullTask.STATUS_PENDING);
        task.setRetryCount(0);
        
        // 验证属性设置
        assertEquals("tiktok", task.getPlatform());
        assertEquals("test_batch_001", task.getBatchNo());
        assertEquals(Integer.valueOf(1), task.getPageNo());
        assertEquals(OrderPullTask.STATUS_PENDING, task.getStatus());
        assertEquals(Integer.valueOf(0), task.getRetryCount());
    }

    @Test
    public void testTaskStatusConstants() {
        // 测试状态常量定义
        assertEquals(0, OrderPullTask.STATUS_PENDING);
        assertEquals(1, OrderPullTask.STATUS_PROCESSING);
        assertEquals(2, OrderPullTask.STATUS_SUCCESS);
        assertEquals(3, OrderPullTask.STATUS_FAILED);
    }

    @Test
    public void testTaskStatusTransitions() {
        // 测试状态转换逻辑
        OrderPullTask task = new OrderPullTask();
        task.setStatus(OrderPullTask.STATUS_PENDING);
        
        // 模拟状态转换
        // 待拉取 -> 拉取中
        task.setStatus(OrderPullTask.STATUS_PROCESSING);
        assertEquals(OrderPullTask.STATUS_PROCESSING, task.getStatus());
        
        // 拉取中 -> 成功
        task.setStatus(OrderPullTask.STATUS_SUCCESS);
        assertEquals(OrderPullTask.STATUS_SUCCESS, task.getStatus());
        
        // 拉取中 -> 失败
        task.setStatus(OrderPullTask.STATUS_FAILED);
        assertEquals(OrderPullTask.STATUS_FAILED, task.getStatus());
    }

    @Test
    public void testRetryLogic() {
        // 测试重试逻辑
        OrderPullTask task = new OrderPullTask();
        task.setRetryCount(0);
        
        // 模拟重试
        task.setRetryCount(task.getRetryCount() + 1);
        assertEquals(Integer.valueOf(1), task.getRetryCount());
        
        task.setRetryCount(task.getRetryCount() + 1);
        assertEquals(Integer.valueOf(2), task.getRetryCount());
    }

    @Test
    public void testTaskEquality() {
        // 测试任务相等性
        OrderPullTask task1 = new OrderPullTask();
        task1.setPlatform("tiktok");
        task1.setBatchNo("test_batch_001");
        task1.setPageNo(1);
        
        OrderPullTask task2 = new OrderPullTask();
        task2.setPlatform("tiktok");
        task2.setBatchNo("test_batch_001");
        task2.setPageNo(1);
        
        // 基本属性相等性测试
        assertEquals(task1.getPlatform(), task2.getPlatform());
        assertEquals(task1.getBatchNo(), task2.getBatchNo());
        assertEquals(task1.getPageNo(), task2.getPageNo());
    }
} 