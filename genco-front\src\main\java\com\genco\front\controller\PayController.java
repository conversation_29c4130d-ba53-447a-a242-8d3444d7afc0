package com.genco.front.controller;

import com.genco.common.request.OrderPayRequest;
import com.genco.common.response.CommonResult;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.utils.CrmebUtil;
import com.genco.service.service.OrderPayService;
import com.genco.service.service.PaymentQueryService;
import com.genco.service.service.WeChatPayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 支付管理 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/front/pay")
@Api(tags = "支付管理")
public class PayController {

    @Autowired
    private WeChatPayService weChatPayService;

    @Autowired
    private OrderPayService orderPayService;

    @Autowired
    private PaymentQueryService paymentQueryService;

    /**
     * 订单支付
     */
    @ApiOperation(value = "订单支付")
    @RequestMapping(value = "/payment", method = RequestMethod.POST)
    public CommonResult<OrderPayResultResponse> payment(@RequestBody @Validated OrderPayRequest orderPayRequest, HttpServletRequest request) {
        String ip = CrmebUtil.getClientIp(request);
        return CommonResult.success(orderPayService.payment(orderPayRequest, ip));
    }

    /**
     * 查询支付结果（兼容旧版本）
     *
     * @param orderNo |订单编号|String|必填
     */
    @ApiOperation(value = "查询支付结果（兼容旧版本）")
    @RequestMapping(value = "/queryPayResult", method = RequestMethod.GET)
    public CommonResult<Boolean> queryPayResult(@RequestParam String orderNo) {
        return CommonResult.success(weChatPayService.queryPayResult(orderNo));
    }

    /**
     * 查询支付结果（新版本，支持多支付渠道）
     *
     * @param orderNo |订单编号|String|必填
     */
    @ApiOperation(value = "查询支付结果（新版本，支持多支付渠道）")
    @RequestMapping(value = "/query", method = RequestMethod.GET)
    public CommonResult<PaymentQueryResultResponse> query(@RequestParam String orderNo) {
        return CommonResult.success(paymentQueryService.queryPaymentResult(orderNo));
    }

    /**
     * 根据支付渠道查询支付结果
     *
     * @param orderNo     |订单编号|String|必填
     * @param payChannel  |支付渠道|String|必填
     */
    @ApiOperation(value = "根据支付渠道查询支付结果")
    @RequestMapping(value = "/queryByChannel", method = RequestMethod.GET)
    public CommonResult<PaymentQueryResultResponse> queryByChannel(@RequestParam String orderNo, @RequestParam String payChannel) {
        return CommonResult.success(paymentQueryService.queryPaymentResult(orderNo, payChannel));
    }
}
