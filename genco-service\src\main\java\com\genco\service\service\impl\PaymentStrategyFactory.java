package com.genco.service.service.impl;

import com.genco.service.service.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付策略工厂
 */
@Slf4j
@Component
public class PaymentStrategyFactory {

    @Autowired
    private List<PaymentStrategy> paymentStrategies;

    private final Map<String, PaymentStrategy> strategyMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (PaymentStrategy strategy : paymentStrategies) {
            strategyMap.put(strategy.getPayChannel(), strategy);
        }
        log.info("支付策略初始化完成，支持的支付类型: {}", strategyMap.keySet());
    }

    /**
     * 根据支付类型获取支付策略
     *
     * @param payChannel 支付类型
     * @return PaymentStrategy
     */
    public PaymentStrategy getStrategy(String payChannel) {
        PaymentStrategy strategy = strategyMap.get(payChannel);
        if (strategy == null) {
            throw new IllegalArgumentException("不支持的支付渠道: " + payChannel);
        }
        return strategy;
    }

    /**
     * 检查是否支持该支付类型
     *
     * @param payChannel 支付渠道
     * @return boolean
     */
    public boolean supports(String payChannel) {
        return strategyMap.containsKey(payChannel);
    }
} 