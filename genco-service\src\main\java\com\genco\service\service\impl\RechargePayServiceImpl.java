package com.genco.service.service.impl;

import com.genco.common.constants.Constants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.finance.UserRecharge;
import com.genco.common.model.user.User;
import com.genco.common.utils.DateUtil;
import com.genco.service.service.RechargePayService;
import com.genco.service.service.UserBillService;
import com.genco.service.service.UserRechargeService;
import com.genco.service.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.Date;


/**
 * 支付类
 */
@Service
public class RechargePayServiceImpl implements RechargePayService {

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserBillService userBillService;

    /**
     * 支付成功处理
     *
     * @param userRecharge 充值订单
     */
    @Override
    public Boolean paySuccess(UserRecharge userRecharge) {
        userRecharge.setPaid(true);
        userRecharge.setPayTime(DateUtil.nowDateTime());

        User user = userService.getById(userRecharge.getUid());

        //判断充值类型，目前充值主要是会员购买，暂没有余额充值
        if (Constants.CONFIG_KEY_AGENT.equals(userRecharge.getRechargeType())) {
            user.setLevel(1);
        } else if (Constants.CONFIG_KEY_PARTNER.equals(userRecharge.getRechargeType())) {
            user.setLevel(4);
        } else {
            throw new CrmebException("暂不支持的充值类型");
        }
        //成为推广员
        user.setIsPromoter(true);
        user.setPromoterTime(new Date());
        Boolean execute = transactionTemplate.execute(e -> {
            // 订单变动
            userRechargeService.updateById(userRecharge);
            // 保存结果
            userService.updateById(user);
            return Boolean.TRUE;
        });
        return execute;
    }
}
