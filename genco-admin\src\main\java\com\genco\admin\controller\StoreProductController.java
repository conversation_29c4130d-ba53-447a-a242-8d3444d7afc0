package com.genco.admin.controller;

import com.genco.common.constants.Constants;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.page.CommonPage;
import com.genco.common.request.*;
import com.genco.common.response.*;
import com.genco.service.service.StoreCartService;
import com.genco.service.service.StoreProductService;
import com.genco.service.service.SystemConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 商品表 前端控制器
 */
@Slf4j
@RestController
@RequestMapping("api/admin/store/product")
@Api(tags = "商品") //配合swagger使用
public class StoreProductController {

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreCartService storeCartService;

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 分页显示商品表
     *
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     */
    @PreAuthorize("hasAuthority('admin:product:list')")
    @ApiOperation(value = "分页列表") //配合swagger使用
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreProductResponse>> getList(@Validated StoreProductSearchRequest request,
                                                                  @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(CommonPage.restPage(storeProductService.getAdminList(request, pageParamRequest)));
    }

    /**
     * 新增商品
     *
     * @param request 新增参数
     */
    @PreAuthorize("hasAuthority('admin:product:save')")
    @ApiOperation(value = "新增商品")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated StoreProductAddRequest request) {
        if (storeProductService.save(request)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 删除商品表
     *
     * @param id Integer
     */
    @PreAuthorize("hasAuthority('admin:product:delete')")
    @ApiOperation(value = "删除")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.GET)
    public CommonResult<String> delete(@RequestBody @PathVariable Integer id, @RequestParam(value = "type", required = false, defaultValue = "recycle") String type) {
        if (storeProductService.deleteProduct(id, type)) {
            if ("recycle".equals(type)) {
                storeCartService.productStatusNotEnable(id);
            } else {
                storeCartService.productDelete(id);
            }
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 恢复已删除商品表
     *
     * @param id Integer
     */
    @PreAuthorize("hasAuthority('admin:product:restore')")
    @ApiOperation(value = "恢复商品")
    @RequestMapping(value = "/restore/{id}", method = RequestMethod.GET)
    public CommonResult<String> restore(@RequestBody @PathVariable Integer id) {
        if (storeProductService.reStoreProduct(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 商品修改
     *
     * @param storeProductRequest 商品参数
     */
    @PreAuthorize("hasAuthority('admin:product:update')")
    @ApiOperation(value = "商品修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated StoreProductAddRequest storeProductRequest) {
        if (storeProductService.update(storeProductRequest)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 商品详情
     *
     * @param id 商品id
     */
    @PreAuthorize("hasAuthority('admin:product:info')")
    @ApiOperation(value = "商品详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<StoreProductInfoResponse> info(@PathVariable Integer id) {
        return CommonResult.success(storeProductService.getInfo(id));
    }

    /**
     * 商品tabs表头数据
     */
    @PreAuthorize("hasAuthority('admin:product:tabs:headers')")
    @ApiOperation(value = "商品表头数量")
    @RequestMapping(value = "/tabs/headers", method = RequestMethod.GET)
    public CommonResult<List<StoreProductTabsHeader>> getTabsHeader() {
        return CommonResult.success(storeProductService.getTabsHeader());
    }


    /**
     * 上架
     */
    @PreAuthorize("hasAuthority('admin:product:up')")
    @ApiOperation(value = "上架")
    @RequestMapping(value = "/putOnShell/{id}", method = RequestMethod.GET)
    public CommonResult<String> putOn(@PathVariable Integer id) {
        if (storeProductService.putOnShelf(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    /**
     * 下架
     */
    @PreAuthorize("hasAuthority('admin:product:down')")
    @ApiOperation(value = "下架")
    @RequestMapping(value = "/offShell/{id}", method = RequestMethod.GET)
    public CommonResult<String> offShell(@PathVariable Integer id) {
        if (storeProductService.offShelf(id)) {
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    @PreAuthorize("hasAuthority('admin:product:import:product')")
    @ApiOperation(value = "导入商品")
    @RequestMapping(value = "/importProduct", method = RequestMethod.POST)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "form", value = "导入平台1=淘宝，2=京东，3=苏宁，4=拼多多, 5=天猫, 6=TikTok", dataType = "int", required = true),
            @ApiImplicitParam(name = "url", value = "URL", dataType = "String", required = true),
    })
    public CommonResult<StoreProductRequest> importProduct(
            @RequestParam @Valid int form,
            @RequestParam @Valid String url) throws IOException, JSONException {
        StoreProductRequest productRequest = storeProductService.importProductFromUrl(url, form);
        return CommonResult.success(productRequest);
    }

    /**
     * 获取复制商品配置
     */
    @PreAuthorize("hasAuthority('admin:product:copy:config')")
    @ApiOperation(value = "获取复制商品配置")
    @RequestMapping(value = "/copy/config", method = RequestMethod.POST)
    public CommonResult<Map<String, Object>> copyConfig() {
        return CommonResult.success(storeProductService.copyConfig());
    }

    @PreAuthorize("hasAuthority('admin:product:copy:product')")
    @ApiOperation(value = "复制平台商品")
    @RequestMapping(value = "/copy/product", method = RequestMethod.POST)
    public CommonResult<Map<String, Object>> copyProduct(@RequestBody @Valid StoreCopyProductRequest request) {
        return CommonResult.success(storeProductService.copyProduct(request.getUrl()));
    }

    /**
     * 品牌商品列表
     * <p>
     * 排序字段（0：默认；1:价格；2:销量；3：新品；4：返现率）
     */
    @PreAuthorize("hasAuthority('admin:product:list')")
    @ApiOperation(value = "品牌商品列表")
    @RequestMapping(value = "/brand/list", method = RequestMethod.GET)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "code", value = "品牌编码", dataType = "String", required = true),
            @ApiImplicitParam(name = "orderBy", value = "排序字段（0：默认；1:价格；2:销量；3：新品；4：返现率）", dataType = "int", required = true),
            @ApiImplicitParam(name = "isAsc", value = "是否升序", dataType = "boolean", required = true)
    })
    public CommonResult<CommonPage<StoreProductResponse>> getBrandProductList(@RequestParam String code,
                                                                              @RequestParam Integer orderBy,
                                                                              @RequestParam Boolean isAsc,
                                                                              PageParamRequest pageParamRequest) {
        List<StoreProduct> storeProductList = storeProductService.getBrandProduct(code, orderBy, isAsc, pageParamRequest);
        if (storeProductList == null || storeProductList.isEmpty()) {
            return CommonResult.success(CommonPage.restPage(new ArrayList<>()));
        }
        BigDecimal platformCashBackRate = new BigDecimal(systemConfigService.getValueByKey(Constants.PLATFORM_CASH_BACK_RATE));
        List<StoreProductResponse> responseList = storeProductList.stream().map(product -> {
            StoreProductResponse response = new StoreProductResponse();
            org.springframework.beans.BeanUtils.copyProperties(product, response);
            if (product.getCashBackAmount() != null) {
                response.setCashBackAmount(product.getCashBackAmount().multiply(platformCashBackRate));
            }
            if (product.getCashBackRate() != null) {
                response.setCashBackRate(product.getCashBackRate().multiply(platformCashBackRate));
            }
            response.setIsIndex(product.getIsHot());
            return response;
        }).collect(Collectors.toList());
        CommonPage<StoreProductResponse> productResponseCommonPage = CommonPage.restPage(responseList);
        org.springframework.beans.BeanUtils.copyProperties(CommonPage.restPage(storeProductList), productResponseCommonPage, "list");
        return CommonResult.success(productResponseCommonPage);
    }

    @PreAuthorize("hasAuthority('admin:product:up')")
    @ApiOperation(value = "批量上架")
    @PostMapping("/batch/putOnShell")
    public CommonResult<List<BatchOperationResult>> batchPutOn(@RequestBody BatchOperationRequest request) {
        List<BatchOperationResult> results = new ArrayList<>();
        for (Integer id : request.getIds()) {
            try {
                boolean success = storeProductService.putOnShelf(id);
                results.add(new BatchOperationResult(id, success, success ? "上架成功" : "上架失败"));
            } catch (Exception e) {
                results.add(new BatchOperationResult(id, false, e.getMessage()));
            }
        }
        return CommonResult.success(results);
    }

    @PreAuthorize("hasAuthority('admin:product:down')")
    @ApiOperation(value = "批量下架")
    @PostMapping("/batch/offShell")
    public CommonResult<List<BatchOperationResult>> batchOffShell(@RequestBody BatchOperationRequest request) {
        List<BatchOperationResult> results = new ArrayList<>();
        for (Integer id : request.getIds()) {
            try {
                boolean success = storeProductService.offShelf(id);
                results.add(new BatchOperationResult(id, success, success ? "下架成功" : "下架失败"));
            } catch (Exception e) {
                results.add(new BatchOperationResult(id, false, e.getMessage()));
            }
        }
        return CommonResult.success(results);
    }

    @PreAuthorize("hasAuthority('admin:product:delete')")
    @ApiOperation(value = "批量删除")
    @PostMapping("/batch/delete")
    public CommonResult<List<BatchOperationResult>> batchDelete(@RequestBody BatchOperationRequest request) {
        String type = request.getType() == null ? "recycle" : request.getType();
        List<BatchOperationResult> results = new ArrayList<>();
        for (Integer id : request.getIds()) {
            try {
                boolean success = storeProductService.deleteProduct(id, type);
                if (success) {
                    if ("recycle".equals(type)) {
                        storeCartService.productStatusNotEnable(id);
                    } else {
                        storeCartService.productDelete(id);
                    }
                    results.add(new BatchOperationResult(id, true, "删除成功"));
                } else {
                    results.add(new BatchOperationResult(id, false, "删除失败"));
                }
            } catch (Exception e) {
                results.add(new BatchOperationResult(id, false, e.getMessage()));
            }
        }
        return CommonResult.success(results);
    }
}



