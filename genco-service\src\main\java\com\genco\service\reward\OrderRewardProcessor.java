package com.genco.service.reward;

import com.genco.common.constants.Constants;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.model.order.StoreOrderInfo;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserBill;
import com.genco.service.model.UserRewardTask;
import com.genco.service.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单返现处理器
 */
@Component
public class OrderRewardProcessor implements UserRewardTaskProcessor {
    @Autowired
    private StoreOrderService storeOrderService;
    @Autowired
    private StoreOrderInfoService storeOrderInfoService;
    @Autowired
    private UserService userService;
    @Autowired
    private UserBillService userBillService;
    @Autowired
    private SystemConfigService systemConfigService;

    @Override
    public boolean supports(String taskType) {
        return UserRewardTask.TASK_TYPE_ORDER_REWARD.equals(taskType);
    }

    @Override
    public void process(UserRewardTask task) {
        // 1. 查询订单
        StoreOrder order = storeOrderService.getByOderId(task.getOrderId());
        if (order == null) throw new RuntimeException("订单不存在，orderId=" + task.getOrderId());
        // 2.获取订单详情
        StoreOrderInfo storeOrderInfo = storeOrderInfoService.getListByOrderNo(order.getOrderId()).get(0);
        // 2.1 获取实际返佣金额
        BigDecimal amount = storeOrderInfo.getActualCommission();
        if (amount == null || amount.compareTo(BigDecimal.ZERO) <= 0)
            throw new RuntimeException("订单金额无效，orderId=" + task.getOrderId());

        // 2. 查询用户及推广链路
        User user = userService.getInfoByUid(order.getUid());
        if (user == null) throw new RuntimeException("用户不存在，uid=" + order.getUid());
        User spread1 = user.getSpreadUid() != null && user.getSpreadUid() > 0 ? userService.getInfoByUid(user.getSpreadUid()) : null;
        User spread2 = (spread1 != null && spread1.getSpreadUid() != null && spread1.getSpreadUid() > 0) ? userService.getInfoByUid(spread1.getSpreadUid()) : null;

        // 3. 获取返现率
        BigDecimal platformRate = getRate(Constants.PLATFORM_CASH_BACK_RATE);
        // 当前用户返现率
        BigDecimal userRate = getUserRate(user);
        BigDecimal user2LRate = getUser2LRate(spread1);
        BigDecimal user3LRate = getUser3LRate(spread2);

        // 4. 返现给当前用户
        BigDecimal cashback = amount.multiply(platformRate).multiply(userRate);
        if (cashback.compareTo(BigDecimal.ZERO) > 0) {
            addBill(user, order, cashback, "一级返现");
        }
        // 5. 返现给二级推广人
        if (spread1 != null) {
            BigDecimal cashback2 = amount.multiply(platformRate).multiply(user2LRate);
            if (cashback2.compareTo(BigDecimal.ZERO) > 0) {
                addBill(spread1, order, cashback2, "二级返现");
            }
        }
        // 6. 返现给三级推广人
        if (spread2 != null) {
            BigDecimal cashback3 = amount.multiply(platformRate).multiply(user3LRate);
            if (cashback3.compareTo(BigDecimal.ZERO) > 0) {
                addBill(spread2, order, cashback3, "三级返现");
            }
        }
    }

    private BigDecimal getRate(String key) {
        String val = systemConfigService.getValueByKey(key);
        try {
            return new BigDecimal(val);
        } catch (Exception e) {
            return BigDecimal.ZERO;
        }
    }

    private BigDecimal getUserRate(User user) {
        if (user == null) {
            return BigDecimal.ZERO;
        }
        int level = user.getLevel() == null ? 0 : user.getLevel();
        if (level == 0) return getRate(Constants.NORMAL_CASH_BACK_RATE);
        if (level >= 1 && level <= 3) return getRate(Constants.AGENT_CASH_BACK_RATE);
        if (level >= 4 && level <= 6) return getRate(Constants.PARTNER_CASH_BACK_RATE);
        return BigDecimal.ZERO;
    }

    private BigDecimal getUser2LRate(User user) {
        if (user == null) {
            return BigDecimal.ZERO;
        }
        int level = user.getLevel() == null ? 0 : user.getLevel();
        if (level == 0) return getRate(Constants.NORMAL_CASH_BACK_RATE_2L);
        if (level >= 1 && level <= 3) return getRate(Constants.AGENT_CASH_BACK_RATE_2L);
        if (level >= 4 && level <= 6) return getRate(Constants.PARTNER_CASH_BACK_RATE_2L);
        return BigDecimal.ZERO;
    }

    private BigDecimal getUser3LRate(User user) {
        if (user == null) {
            return BigDecimal.ZERO;
        }
        int level = user.getLevel() == null ? 0 : user.getLevel();
        if (level == 0) return getRate(Constants.NORMAL_CASH_BACK_RATE_3L);
        if (level >= 1 && level <= 3) return getRate(Constants.AGENT_CASH_BACK_RATE_3L);
        if (level >= 4 && level <= 6) return getRate(Constants.PARTNER_CASH_BACK_RATE_3L);
        return BigDecimal.ZERO;
    }

    private void addBill(User user, StoreOrder order, BigDecimal cashback, String mark) {
        UserBill bill = new UserBill();
        bill.setUid(user.getUid());
        bill.setLinkId(order.getOrderId());
        bill.setPm(1);
        bill.setTitle("订单返现");
        bill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
        bill.setType("order_cashback");
        bill.setNumber(cashback);
        bill.setBalance(user.getNowMoney() != null ? user.getNowMoney().add(cashback) : cashback);
        bill.setMark(mark);
        bill.setStatus(1);
        bill.setCreateTime(new Date());
        boolean ok = userBillService.save(bill);
        if (!ok) throw new RuntimeException("账单写入失败，uid=" + user.getUid() + ", orderId=" + order.getOrderId());
    }
} 