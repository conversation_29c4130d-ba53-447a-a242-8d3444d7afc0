package com.genco.service.service;

import com.genco.common.response.PaymentQueryResultResponse;

/**
 * 支付查询服务接口
 */
public interface PaymentQueryService {

    /**
     * 查询支付结果
     *
     * @param orderNo 订单编号
     * @return PaymentQueryResultResponse
     */
    PaymentQueryResultResponse queryPaymentResult(String orderNo);

    /**
     * 根据支付渠道查询支付结果
     *
     * @param orderNo    订单编号
     * @param payChannel 支付渠道
     * @return PaymentQueryResultResponse
     */
    PaymentQueryResultResponse queryPaymentResult(String orderNo, String payChannel);
} 