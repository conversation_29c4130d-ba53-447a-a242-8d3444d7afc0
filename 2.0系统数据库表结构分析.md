# 2.0系统数据库表结构分析

## 数据库基本信息
- **数据库名称**: jglife
- **主机地址**: rm-wz9y8u39xm73w5942ro.mysql.rds.aliyuncs.com
- **用户名**: jglife
- **总表数量**: 119张表

## 表分类概览

### 1. 用户相关表 (User Related)
- `eb_user` - 用户主表
- `eb_user_address` - 用户地址
- `eb_user_balance_record` - 用户余额记录
- `eb_user_bill` - 用户账单
- `eb_user_brokerage_record` - 用户佣金记录
- `eb_user_closing` - 用户结算
- `eb_user_experience_record` - 用户经验记录
- `eb_user_integral_record` - 用户积分记录
- `eb_user_level` - 用户等级
- `eb_user_merchant_collect` - 用户商户收藏
- `eb_user_sign` - 用户签到
- `eb_user_sign_record` - 用户签到记录
- `eb_user_tag` - 用户标签
- `eb_user_token` - 用户令牌
- `eb_user_visit_record` - 用户访问记录

### 2. 商品相关表 (Product Related)
- `eb_product` - 商品主表
- `eb_product_attr_value` - 商品属性值
- `eb_product_attribute` - 商品属性
- `eb_product_attribute_option` - 商品属性选项
- `eb_product_brand` - 商品品牌
- `eb_product_brand_category` - 商品品牌分类
- `eb_product_category` - 商品分类
- `eb_product_coupon` - 商品优惠券
- `eb_product_day_record` - 商品日记录
- `eb_product_description` - 商品描述
- `eb_product_guarantee` - 商品保障
- `eb_product_guarantee_group` - 商品保障组
- `eb_product_relation` - 商品关联
- `eb_product_reply` - 商品评价
- `eb_product_rule` - 商品规则
- `eb_product_tag` - 商品标签

### 3. 订单相关表 (Order Related)
- `eb_order` - 订单主表
- `eb_order_detail` - 订单详情
- `eb_order_invoice` - 订单发票
- `eb_order_invoice_detail` - 订单发票详情
- `eb_order_profit_sharing` - 订单分润
- `eb_order_status` - 订单状态

### 4. 营销活动表 (Marketing Related)
- `eb_activity_style` - 活动样式
- `eb_coupon` - 优惠券
- `eb_coupon_product` - 优惠券商品
- `eb_coupon_user` - 用户优惠券
- `eb_group_buy_activity` - 团购活动
- `eb_group_buy_activity_sku` - 团购活动SKU
- `eb_group_config` - 团购配置
- `eb_seckill_activity` - 秒杀活动
- `eb_seckill_activity_time` - 秒杀活动时间
- `eb_seckill_product` - 秒杀商品
- `eb_seckill_time_interval` - 秒杀时间间隔

### 5. 支付相关表 (Payment Related)
- `eb_ali_pay_callback` - 支付宝回调
- `eb_ali_pay_info` - 支付宝信息
- `eb_bill` - 账单
- `eb_recharge_order` - 充值订单
- `eb_refund_order` - 退款订单
- `eb_refund_order_info` - 退款订单信息
- `eb_refund_order_status` - 退款订单状态

### 6. 系统管理表 (System Management)
- `eb_system_admin` - 系统管理员
- `eb_system_attachment` - 系统附件
- `eb_system_config` - 系统配置
- `eb_system_form` - 系统表单
- `eb_system_form_data_record` - 系统表单数据记录
- `eb_system_form_temp` - 系统表单模板
- `eb_system_group` - 系统分组
- `eb_system_group_data` - 系统分组数据
- `eb_system_menu` - 系统菜单
- `eb_system_notification` - 系统通知
- `eb_system_role` - 系统角色
- `eb_system_role_menu` - 系统角色菜单
- `eb_system_user_level` - 系统用户等级

### 7. 社区相关表 (Community Related)
- `eb_community_author_concerned` - 社区作者关注
- `eb_community_category` - 社区分类
- `eb_community_notes` - 社区笔记
- `eb_community_notes_product` - 社区笔记商品
- `eb_community_notes_relation` - 社区笔记关联
- `eb_community_reply` - 社区回复
- `eb_community_reply_like` - 社区回复点赞
- `eb_community_topic` - 社区话题

### 8. 微信相关表 (WeChat Related)
- `eb_wechat_callback` - 微信回调
- `eb_wechat_exceptions` - 微信异常
- `eb_wechat_live_assistant` - 微信直播助手
- `eb_wechat_live_goods` - 微信直播商品

## 核心表结构详情

### eb_user (用户表)
- **主键**: id (int unsigned, auto_increment)
- **核心字段**:
  - account: 账号 (varchar(32))
  - pwd: 密码 (varchar(32))
  - real_name: 真实姓名 (varchar(25))
  - nickname: 昵称 (varchar(255))
  - phone: 手机号 (varchar(50))
  - avatar: 头像 (varchar(256))
  - now_money: 余额 (decimal(16,2))
  - integral: 积分 (int)
  - level: 等级 (tinyint)
  - is_promoter: 是否推广员 (tinyint(1))
  - spread_uid: 推荐人ID (int)
  - is_paid_member: 是否付费会员 (tinyint(1))
  - paid_member_expiration_time: 付费会员到期时间 (datetime)

### eb_product (商品表)
- **主键**: id (int, auto_increment)
- **核心字段**:
  - mer_id: 商户ID (int)
  - name: 商品名称 (varchar(128))
  - image: 商品图片 (varchar(256))
  - price: 价格 (decimal(8,2))
  - vip_price: VIP价格 (decimal(8,2))
  - stock: 库存 (int unsigned)
  - sales: 销量 (int unsigned)
  - cate_id: 分类ID (varchar(64))
  - brand_id: 品牌ID (int)
  - is_show: 是否显示 (tinyint(1))
  - audit_status: 审核状态 (int unsigned)
  - type: 商品类型 (int)
  - marketing_type: 营销类型 (int)

### eb_order (订单表)
- **主键**: id (int unsigned, auto_increment)
- **核心字段**:
  - order_no: 订单号 (varchar(32))
  - uid: 用户ID (int unsigned)
  - mer_id: 商户ID (int unsigned)
  - total_price: 总价 (decimal(8,2))
  - pay_price: 实付金额 (decimal(8,2))
  - paid: 是否支付 (tinyint unsigned)
  - pay_type: 支付方式 (varchar(32))
  - status: 订单状态 (tinyint)
  - refund_status: 退款状态 (tinyint)
  - type: 订单类型 (int)

### eb_system_admin (系统管理员表)
- **主键**: id (smallint unsigned, auto_increment)
- **核心字段**:
  - account: 账号 (varchar(32))
  - pwd: 密码 (char(32))
  - real_name: 真实姓名 (varchar(16))
  - roles: 角色 (varchar(128))
  - level: 等级 (tinyint unsigned)
  - type: 类型 (int)
  - mer_id: 商户ID (int)

## 特色功能分析

### 1. 多端支持
- 支持微信公众号、小程序、iOS、Android等多端
- 用户表中有相应的标识字段

### 2. 会员体系
- 付费会员功能完善
- 支持永久会员和有期限会员
- VIP价格体系

### 3. 营销功能丰富
- 团购、秒杀、优惠券等多种营销方式
- 推广员分佣体系
- 积分兑换功能

### 4. 社区功能
- 完整的社区笔记、评论、点赞功能
- 作者关注机制

### 5. 商户多级管理
- 支持多商户模式
- 商户商品分类管理
- 商户保障体系

### 6. 完善的财务体系
- 详细的账单记录
- 分润机制
- 财务报表统计

## 数据库设计特点

1. **统一前缀**: 所有表都使用 `eb_` 前缀
2. **时间戳**: 大部分表都有 `create_time` 和 `update_time` 字段
3. **软删除**: 使用 `is_del` 字段实现软删除
4. **状态管理**: 多种状态字段管理不同业务状态
5. **扩展性**: 预留了多个扩展字段和类型字段

## 完整表列表 (119张表)

### 9. 其他功能表 (Other Features)
- `eb_article` - 文章
- `eb_article_category` - 文章分类
- `eb_browse_record` - 浏览记录
- `eb_card_secret` - 卡密
- `eb_cart` - 购物车
- `eb_category` - 分类
- `eb_cdkey_library` - CDKEY库
- `eb_city_region` - 城市区域
- `eb_exception_log` - 异常日志
- `eb_express` - 快递
- `eb_merchant_product_category` - 商户商品分类
- `eb_merchant_product_guarantee_group` - 商户商品保障组
- `eb_merchant_type` - 商户类型
- `eb_page_category` - 页面分类
- `eb_page_diy` - 页面DIY
- `eb_page_link` - 页面链接
- `eb_paid_member_card` - 付费会员卡
- `eb_paid_member_order` - 付费会员订单
- `eb_pay_component_brand` - 支付组件品牌
- `eb_pay_component_cat` - 支付组件分类
- `eb_pay_component_delivery_company` - 支付组件快递公司
- `eb_pay_component_draft_product` - 支付组件草稿商品
- `eb_pay_component_order` - 支付组件订单
- `eb_pay_component_order_product` - 支付组件订单商品
- `eb_pay_component_product` - 支付组件商品
- `eb_pay_component_product_audit_info` - 支付组件商品审核信息
- `eb_pay_component_product_info` - 支付组件商品信息
- `eb_pay_component_product_sku` - 支付组件商品SKU
- `eb_pay_component_product_sku_attr` - 支付组件商品SKU属性
- `eb_pay_component_shop_brand` - 支付组件店铺品牌
- `eb_platform_daily_statement` - 平台日报表
- `eb_platform_month_statement` - 平台月报表
- `eb_schedule_job` - 定时任务
- `eb_schedule_job_log` - 定时任务日志
- `eb_sensitive_method_log` - 敏感方法日志
- `eb_shipping_templates` - 运费模板
- `eb_shipping_templates_free` - 运费模板免费
- `eb_shipping_templates_region` - 运费模板区域
- `eb_shopping_product_day_record` - 购物商品日记录
- `eb_sign_config` - 签到配置
- `eb_sms_template` - 短信模板
- `eb_summary_financial_statements` - 汇总财务报表
- `eb_template_message` - 模板消息
- `eb_trading_day_record` - 交易日记录
- `eb_verification_record` - 验证记录

## 业务功能模块分析

### 1. 电商核心功能
- **商品管理**: 支持多规格商品、品牌分类、保障服务
- **订单流程**: 完整的下单、支付、发货、收货、评价流程
- **库存管理**: 实时库存跟踪、销量统计
- **价格体系**: 普通价格、VIP价格、成本价格

### 2. 用户体系
- **注册登录**: 支持多种注册方式
- **会员等级**: 普通用户、VIP会员、永久会员
- **积分体系**: 积分获取、消费、兑换
- **推广分佣**: 推广员机制、佣金结算

### 3. 营销推广
- **优惠券**: 平台券、商户券、商品券
- **团购活动**: 拼团功能、团购配置
- **秒杀活动**: 限时秒杀、时间段管理
- **活动样式**: 自定义活动展示

### 4. 支付结算
- **多支付方式**: 支付宝、微信支付等
- **退款管理**: 退款申请、审核、处理
- **财务报表**: 日报、月报、汇总报表
- **分润机制**: 平台、商户、推广员分润

### 5. 内容社区
- **社区笔记**: 用户发布笔记、图文内容
- **互动功能**: 评论、点赞、关注
- **商品关联**: 笔记关联商品推荐
- **话题管理**: 社区话题分类

### 6. 系统管理
- **权限管理**: 角色权限、菜单权限
- **配置管理**: 系统参数配置
- **日志管理**: 操作日志、异常日志
- **定时任务**: 自动化任务调度

## 技术特点

### 1. 微服务架构支持
- 表结构设计支持分布式部署
- 统一的数据标准和接口规范

### 2. 多端适配
- 支持Web、小程序、APP等多端
- 统一的用户体系和数据同步

### 3. 扩展性设计
- 预留扩展字段便于功能升级
- 模块化设计支持功能插拔

### 4. 性能优化
- 合理的索引设计
- 分表分库准备
- 缓存友好的数据结构

## 待补充信息

由于1.0系统数据库连接超时，无法获取其表结构进行对比分析。建议：

1. 提供1.0系统的数据库导出文件
2. 或提供1.0系统的表结构文档
3. 确认1.0系统数据库连接配置

完成1.0系统信息收集后，可以进行详细的版本对比分析，包括：
- 新增表和功能
- 表结构变更
- 字段变更和数据迁移建议
- 业务功能差异分析
