package com.genco.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.service.dao.tiktok.OrderPullTaskDao;
import com.genco.service.model.tiktok.OrderPullProgress;
import com.genco.service.model.tiktok.OrderPullTask;
import com.genco.service.service.OrderPullProgressService;
import com.genco.service.service.OrderPullTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tiktokshop.open.sdk_java.invoke.ApiClient;

import java.util.Date;
import com.genco.service.model.tiktok.OrderPullTaskMessage;
import com.genco.common.utils.RedisUtil;
import com.alibaba.fastjson.JSON;

/**
 * 多平台订单分页拉取任务表 Service实现类
 */
@Service
public class OrderPullTaskServiceImpl extends ServiceImpl<OrderPullTaskDao, OrderPullTask> implements OrderPullTaskService {

    @Autowired
    private OrderPullProgressService orderPullProgressService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 生成指定时间区间和批次号下的所有分页任务，支持断点续生成和幂等插入
     *
     * @param startTime 区间起始时间
     * @param endTime   区间结束时间
     * @param batchNo   批次号
     * @param apiClient 已配置好token等的ApiClient
     */
    public void generateAllPageTasks(Date startTime, Date endTime, String batchNo, ApiClient apiClient) {
        // 查找进度
        OrderPullProgress progress = orderPullProgressService.lambdaQuery()
                .eq(OrderPullProgress::getStartTime, startTime)
                .eq(OrderPullProgress::getEndTime, endTime)
                .eq(OrderPullProgress::getBatchNo, batchNo)
                .one();
        String pageToken = progress != null ? progress.getLastPageToken() : null;
        int pageNo = progress != null ? progress.getLastPageNo() + 1 : 1;
        boolean finished = false;
        try {
            while (!finished) {
                // 拉取一页，仅获取nextPageToken
                tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api affiliateApi = new tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api(apiClient);
                tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.SearchCreatorAffiliateOrdersRequestBody requestBody = new tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.SearchCreatorAffiliateOrdersRequestBody();
                if (startTime != null && endTime != null) {
                    requestBody.setCreateTimeGe(startTime.getTime() / 1000);
                    requestBody.setCreateTimeLt(endTime.getTime() / 1000);
                }
                tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.SearchCreatorAffiliateOrdersResponse resp = affiliateApi.affiliateCreator202410OrdersSearchPost(
                        20L, apiClient.getTokens(), "application/json", pageToken, requestBody);
                String nextPageToken = null;
                if (resp != null && resp.getCode() != null && resp.getCode() == 0) {
                    nextPageToken = resp.getData().getNextPageToken();
                }
                // 幂等插入任务
                boolean exists = this.lambdaQuery()
                        .eq(OrderPullTask::getStartTime, startTime)
                        .eq(OrderPullTask::getEndTime, endTime)
                        .eq(OrderPullTask::getBatchNo, batchNo)
                        .eq(OrderPullTask::getPageNo, pageNo)
                        .count() > 0;
                if (!exists) {
                    OrderPullTask task = new OrderPullTask();
                    task.setStartTime(startTime);
                    task.setEndTime(endTime);
                    task.setBatchNo(batchNo);
                    task.setPageNo(pageNo);
                    task.setNextPageToken(pageToken);
                    task.setStatus(0);
                    task.setRetryCount(0);
                    task.setCreateTime(new Date());
                    task.setUpdateTime(new Date());
                    this.save(task);
                    // 生成并分发任务
                    generateAndDispatchTask(task);
                }
                // 更新进度表
                if (progress == null) {
                    progress = new OrderPullProgress();
                    progress.setStartTime(startTime);
                    progress.setEndTime(endTime);
                    progress.setBatchNo(batchNo);
                }
                progress.setLastPageNo(pageNo);
                progress.setLastPageToken(nextPageToken);
                progress.setStatus(0);
                progress.setUpdateTime(new Date());
                orderPullProgressService.saveOrUpdate(progress);
                if (nextPageToken == null) {
                    finished = true;
                    progress.setStatus(1); // 完成
                    orderPullProgressService.saveOrUpdate(progress);
                } else {
                    pageToken = nextPageToken;
                    pageNo++;
                }
            }
        } catch (Exception e) {
            if (progress != null) {
                progress.setStatus(2); // 失败
                orderPullProgressService.saveOrUpdate(progress);
            }
            throw new RuntimeException("生成分页任务失败: " + e.getMessage(), e);
        }
    }

    public void generateAndDispatchTask(OrderPullTask task) {
        // 1. 保存任务表记录
        this.save(task);
        // 2. 构造消息体
        OrderPullTaskMessage msg = new OrderPullTaskMessage();
        msg.setPlatform(task.getPlatform());
        msg.setStartTime(task.getStartTime());
        msg.setEndTime(task.getEndTime());
        msg.setBatchNo(task.getBatchNo());
        msg.setPageNo(task.getPageNo());
        msg.setNextPageToken(task.getNextPageToken());
        // 3. 推送到Redis队列
        String queueKey = "order_pull_task_queue:" + task.getPlatform();
        redisUtil.lPush(queueKey, JSON.toJSONString(msg));
    }

    @Override
    public boolean manualRetry(Long taskId) {
        OrderPullTask task = this.getById(taskId);
        if (task == null) return false;
        try {
            // 这里假设有TiktokOrderSyncService可用，实际应注入并调用
            // String nextPageToken = tiktokOrderSyncService.syncTiktokOrders(task.getNextPageToken(), task.getStartTime(), task.getEndTime());
            // 这里只做状态更新示例，实际应补偿拉取逻辑
            this.lambdaUpdate()
                .set(OrderPullTask::getStatus, 2)
                .set(OrderPullTask::getUpdateTime, new Date())
                .eq(OrderPullTask::getId, task.getId())
                .update();
            return true;
        } catch (Exception e) {
            return false;
        }
    }
} 