package com.genco.common.vo;

import lombok.Data;

/**
 * HaiPay支付响应VO
 */
@Data
public class HaiPayResponseVo {

    /**
     * 状态
     */
    private String status;

    /**
     * 错误码
     */
    private String error;

    /**
     * 错误信息
     */
    private String msg;

    /**
     * 响应数据
     */
    private HaiPayDataVo data;

    /**
     * HaiPay响应数据
     */
    @Data
    public static class HaiPayDataVo {

        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 支付URL
         */
        private String payUrl;

        /**
         * 银行代码
         */
        private String bankCode;

        /**
         * 银行号
         */
        private String bankNo;

        /**
         * 二维码
         */
        private String qrCode;
        
        /**
         * 签名
         */
        private String sign;

    }
} 