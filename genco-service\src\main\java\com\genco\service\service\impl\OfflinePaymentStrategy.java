package com.genco.service.service.impl;

import com.genco.common.constants.PayConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.vo.PaymentRequestVo;
import com.genco.service.service.PaymentStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 线下支付策略实现
 */
@Slf4j
@Service
public class OfflinePaymentStrategy implements PaymentStrategy {

    @Override
    public OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest) {
        throw new CrmebException("暂时不支持线下支付");
    }

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        log.info("线下支付查询支付结果开始，订单号：{}", orderNo);

        try {
            // 根据订单号前缀判断业务类型
            String bizType = "unknown";
            if (orderNo.startsWith("wxNo") || orderNo.startsWith("order")) {
                bizType = PayConstants.BIZ_TYPE_ORDER;
            } else if (orderNo.startsWith("recharge")) {
                bizType = PayConstants.BIZ_TYPE_RECHARGE;
            }

            // 构建返回结果
            PaymentQueryResultResponse result = new PaymentQueryResultResponse();
            result.setOrderNo(orderNo);
            result.setOutTradeNo(orderNo);
            result.setPayChannel(PayConstants.PAY_TYPE_OFFLINE);
            result.setBizType(bizType);
            result.setPaid(false);
            result.setAmount(BigDecimal.ZERO);

            log.info("线下支付查询支付结果成功，订单号：{}，支付状态：{}", orderNo, result.getPaid());
            return result;

        } catch (Exception e) {
            log.error("线下支付查询支付结果异常，订单号：{}", orderNo, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }

    @Override
    public String getPayChannel() {
        return PayConstants.PAY_TYPE_OFFLINE;
    }
} 