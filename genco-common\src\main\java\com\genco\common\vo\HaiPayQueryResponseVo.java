package com.genco.common.vo;

import lombok.Data;

/**
 * HaiPay查询响应VO
 */
@Data
public class HaiPayQueryResponseVo {

    /**
     * 状态(0未开始，1支付中，2成功（终态），3失败（终态）, -1异常待确认)
     */
    private String status;

    /**
     * 错误码
     */
    private String error;

    /**
     * 错误信息
     */
    private String msg;

    /**
     * 响应数据
     */
    private HaiPayQueryDataVo data;


    /**
     * HaiPay查询响应数据
     */
    @Data
    public static class HaiPayQueryDataVo {

        /**
         * 订单ID
         */
        private String orderId;

        /**
         * 订单号
         */
        private String orderNo;

        /**
         * 状态(0未开始，1支付中，2成功（终态），3失败（终态）, -1异常待确认)
         */
        private String status;

        /**
         * 支付金额
         */
        private String amount;

        /**
         * 实际收到金额
         */
        private String actualAmount;

        /**
         * 手续费
         */
        private String fee;

        /**
         * 支付时间
         */
        private String payTime;

        /**
         * 银行代码
         */
        private String bankCode;

        /**
         * 银行号
         */
        private String bankNo;

        /**
         * 交易流水号
         */
        private String transactionId;

        /**
         * 签名
         */
        private String sign;
    }
} 