package com.genco.service.service.impl;

import com.genco.service.service.PlatformOrderSyncService;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class ShopeeOrderSyncServiceImpl implements PlatformOrderSyncService {
    @Override
    public String syncOrders(String pageToken, Date startTime, Date endTime) {
        // TODO: 实现Shopee订单同步逻辑
        return null;
    }

    @Override
    public String getPlatform() {
        return "shopee";
    }
} 