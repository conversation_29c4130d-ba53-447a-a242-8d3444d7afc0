package com.genco.service.scheduler;

import com.genco.service.model.UserRewardTask;
import com.genco.service.reward.UserRewardTaskProcessor;
import com.genco.service.service.UserRewardTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 用户奖励/返佣任务定时调度服务。
 * 定时拉取待处理任务，根据类型分发到不同处理器。
 */
@Component
public class UserRewardTaskScheduler {
    private static final Logger logger = LoggerFactory.getLogger(UserRewardTaskScheduler.class);

    @Autowired
    private UserRewardTaskService userRewardTaskService;

    @Autowired
    private List<UserRewardTaskProcessor> processors;

    /**
     * 每分钟执行一次，处理奖励任务。
     */
    @Scheduled(initialDelay = 10000, fixedDelay = 60000)
    public void processTasks() {
        try {
            List<UserRewardTask> tasks = userRewardTaskService.getPendingTasks(50);
            if (tasks == null || tasks.isEmpty()) return;
            for (UserRewardTask task : tasks) {
                boolean processed = false;
                for (UserRewardTaskProcessor processor : processors) {
                    if (processor.supports(task.getTaskType())) {
                        try {
                            // 标记处理中
                            task.setStatus("PROCESSING");
                            userRewardTaskService.updateTask(task);
                            processor.process(task);
                            // 标记完成
                            task.setStatus("COMPLETED");
                            userRewardTaskService.updateTask(task);
                            processed = true;
                            logger.info("任务处理成功，id={}, type={}", task.getId(), task.getTaskType());
                        } catch (Exception e) {
                            // 标记失败
                            task.setStatus("FAILED");
                            userRewardTaskService.updateTask(task);
                            logger.error("任务处理失败，id={}, type={}, error={}", task.getId(), task.getTaskType(), e.getMessage(), e);
                        }
                        break;
                    }
                }
                if (!processed) {
                    logger.warn("处理失败，任务id={}, type={}", task.getId(), task.getTaskType());
                }
            }
        } catch (Exception e) {
            logger.error("用户奖励处理定时任务执行失败", e);
        }
    }
} 