package com.genco.service.service;

import com.genco.common.vo.HaiPayRequestVo;
import com.genco.common.vo.HaiPayResponseVo;
import com.genco.common.vo.HaiPayQueryRequestVo;
import com.genco.common.vo.HaiPayQueryResponseVo;

/**
 * HaiPay支付服务接口
 */
public interface HaiPayService {

    /**
     * 创建支付订单
     *
     * @param request 支付请求
     * @return 支付响应
     */
    HaiPayResponseVo createPayment(HaiPayRequestVo request);

    /**
     * 查询支付结果
     *
     * @param request 查询请求
     * @return 查询响应
     */
    HaiPayQueryResponseVo queryPayment(HaiPayQueryRequestVo request);

    /**
     * 验证签名
     *
     * @param data 数据
     * @param sign 签名
     * @return 是否验证通过
     */
    boolean verifySign(String data, String sign);
} 