package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.record.UserVisitRecord;

/**
 * UserVisitRecordService 接口
 */
public interface UserVisitRecordService extends IService<UserVisitRecord> {

    /**
     * 通过日期获取浏览量
     *
     * @param date 日期
     * @return Integer
     */
    Integer getPageviewsByDate(String date);

    /**
     * 通过时间段获取浏览量
     *
     * @param startDate 日期
     * @param endDate   日期
     * @return Integer
     */
    Integer getPageviewsByPeriod(String startDate, String endDate);

    /**
     * 通过日期获取活跃用户数
     *
     * @param date 日期
     * @return Integer
     */
    Integer getActiveUserNumByDate(String date);

    /**
     * 通过时间段获取活跃用户数
     *
     * @param startDate 日期
     * @param endDate   日期
     * @return Integer
     */
    Integer getActiveUserNumByPeriod(String startDate, String endDate);
}
