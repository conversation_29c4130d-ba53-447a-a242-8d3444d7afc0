package com.genco.service.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.genco.common.constants.PayConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.utils.HaiPaySignUtils;
import com.genco.common.utils.SHA256WithRSAUtils;
import com.genco.common.vo.HaiPayQueryRequestVo;
import com.genco.common.vo.HaiPayQueryResponseVo;
import com.genco.common.vo.HaiPayRequestVo;
import com.genco.common.vo.HaiPayResponseVo;
import com.genco.service.service.HaiPayService;
import com.genco.service.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * HaiPay支付服务实现类
 */
@Slf4j
@Service
public class HaiPayServiceImpl implements HaiPayService {

    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 发起支付请求
     *
     * @param request 支付请求
     * @return HaiPayResponseVo
     */
    @Override
    public HaiPayResponseVo createPayment(HaiPayRequestVo request) {
        try {
            // 获取配置
            String privateKey = systemConfigService.getValueByKey("haipay_private_key");
            String secretKey = systemConfigService.getValueByKey("haipay_secret_key");

            if (StrUtil.isBlank(privateKey) || StrUtil.isBlank(secretKey)) {
                throw new CrmebException("HaiPay配置不完整，请检查私钥和密钥配置");
            }

            // 生成签名
            if (request == null) {
                throw new CrmebException("HaiPay支付请求参数不能为空");
            }
            
            String signContent;
            try {
                signContent = HaiPaySignUtils.getHaiPayRequestSign(request, secretKey);
            } catch (Exception e) {
                log.error("生成签名失败，请求参数: {}", JSONUtil.toJsonStr(request), e);
                throw new CrmebException("生成签名失败: " + e.getMessage());
            }
            
            String sign = SHA256WithRSAUtils.buildRSASignByPrivateKey(signContent, privateKey);
            request.setSign(sign);

            log.info("HaiPay支付请求参数: {}", JSONUtil.toJsonStr(request));

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(PayConstants.HAI_PAY_API_URL)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(request))
                    .timeout(30000)
                    .execute();

            if (!response.isOk()) {
                throw new CrmebException("HaiPay API请求失败，HTTP状态码: " + response.getStatus());
            }

            String responseBody = response.body();
            log.info("HaiPay支付响应: {}", responseBody);

            HaiPayResponseVo responseVo = JSONUtil.toBean(responseBody, HaiPayResponseVo.class);

            // 检查响应对象是否为空
            if (responseVo == null) {
                throw new CrmebException("HaiPay API响应为空");
            }

            // 检查响应数据是否为空
            if (responseVo.getData() == null) {
                throw new CrmebException("HaiPay API响应数据为空");
            }

            // 验证签名
            String rSign = responseVo.getData().getSign();
            if (StrUtil.isBlank(rSign)) {
                log.error("HaiPay支付API请求失败：签名为空");
                throw new CrmebException("HaiPay支付API请求失败：签名为空");
            }

            // 使用 SignUtils 生成签名字符串（自动过滤 sign/sign_type，自动排序，拼接）
            String signData;
            try {
                signData = HaiPaySignUtils.getHaiPayResponseSign(responseVo.getData(), secretKey);
            } catch (Exception e) {
                log.error("生成响应签名失败，响应数据: {}", JSONUtil.toJsonStr(responseVo.getData()), e);
                throw new CrmebException("生成响应签名失败: " + e.getMessage());
            }

            // 验证签名
            if (!verifySign(signData, rSign)) {
                log.error("HaiPay支付签名验证失败");
                throw new CrmebException("HaiPay支付签名验证失败");
            }

            // 检查API调用状态
            if (!PayConstants.API_STATUS_SUCCESS.equals(responseVo.getStatus())) {
                throw new CrmebException("HaiPay支付失败: " + responseVo.getMsg());
            }

            return responseVo;

        } catch (Exception e) {
            log.error("HaiPay支付创建失败", e);
            throw new CrmebException("HaiPay支付创建失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付结果
     *
     * @param request 查询请求
     * @return HaiPayQueryResponseVo
     */
    @Override
    public HaiPayQueryResponseVo queryPayment(HaiPayQueryRequestVo request) {
        try {
            // 获取配置
            String privateKey = systemConfigService.getValueByKey("haipay_private_key");
            String secretKey = systemConfigService.getValueByKey("haipay_secret_key");

            if (StrUtil.isBlank(privateKey) || StrUtil.isBlank(secretKey)) {
                throw new CrmebException("HaiPay配置不完整，请检查私钥和密钥配置");
            }

            // 生成签名
            if (request == null) {
                throw new CrmebException("HaiPay查询请求参数不能为空");
            }
            
            String signContent;
            try {
                signContent = HaiPaySignUtils.getHaiPayQueryRequestSign(request, secretKey);
            } catch (Exception e) {
                log.error("生成查询签名失败，请求参数: {}", JSONUtil.toJsonStr(request), e);
                throw new CrmebException("生成查询签名失败: " + e.getMessage());
            }
            
            String sign = SHA256WithRSAUtils.buildRSASignByPrivateKey(signContent, privateKey);
            request.setSign(sign);

            log.info("HaiPay查询请求参数: {}", JSONUtil.toJsonStr(request));

            // 发送HTTP请求
            HttpResponse response = HttpRequest.post(PayConstants.HAI_PAY_QUERY_API_URL)
                    .header("Content-Type", "application/json")
                    .body(JSONUtil.toJsonStr(request))
                    .timeout(30000)
                    .execute();

            if (!response.isOk()) {
                throw new CrmebException("HaiPay查询API请求失败，HTTP状态码: " + response.getStatus());
            }

            String responseBody = response.body();
            log.info("HaiPay查询响应: {}", responseBody);

            HaiPayQueryResponseVo responseVo = JSONUtil.toBean(responseBody, HaiPayQueryResponseVo.class);

            // 检查响应对象是否为空
            if (responseVo == null) {
                throw new CrmebException("HaiPay查询API响应为空");
            }

            // 检查响应数据是否为空
            if (responseVo.getData() == null) {
                throw new CrmebException("HaiPay查询API响应数据为空");
            }

            // 验证签名
            String rSign = responseVo.getData().getSign();
            if (StrUtil.isBlank(rSign)) {
                log.error("HaiPay查询API请求失败：签名为空");
                throw new CrmebException("HaiPay查询API请求失败：签名为空");
            }

            // 使用 SignUtils 生成签名字符串（自动过滤 sign/sign_type，自动排序，拼接）
            String signData;
            try {
                signData = HaiPaySignUtils.getHaiPayQueryResponseSign(responseVo.getData(), secretKey);
            } catch (Exception e) {
                log.error("生成查询响应签名失败，响应数据: {}", JSONUtil.toJsonStr(responseVo.getData()), e);
                throw new CrmebException("生成查询响应签名失败: " + e.getMessage());
            }

            // 验证签名
            if (!verifySign(signData, rSign)) {
                log.error("HaiPay查询签名验证失败");
                throw new CrmebException("HaiPay查询签名验证失败");
            }

            // 检查API调用状态
            if (!PayConstants.API_STATUS_SUCCESS.equals(responseVo.getStatus())) {
                throw new CrmebException("HaiPay查询API调用失败: " + responseVo.getMsg());
            }

            return responseVo;

        } catch (Exception e) {
            log.error("HaiPay查询失败", e);
            throw new CrmebException("HaiPay查询失败: " + e.getMessage());
        }
    }

    @Override
    public boolean verifySign(String data, String sign) {
        try {
            String publicKey = systemConfigService.getValueByKey("haipay_public_key");
            if (StrUtil.isBlank(publicKey)) {
                log.error("HaiPay公钥未配置");
                return false;
            }
            return SHA256WithRSAUtils.buildRSAverifyByPublicKey(data, publicKey, sign);
        } catch (Exception e) {
            log.error("HaiPay签名验证失败", e);
            return false;
        }
    }
} 