package com.genco.service.service.impl;

import com.genco.service.dao.UserRewardTaskDao;
import com.genco.service.model.UserRewardTask;
import com.genco.service.service.UserRewardTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.List;

/**
 * 用户奖励/返佣任务服务实现。
 * 支持任务表写入和后续MQ扩展。
 */
@Service
public class UserRewardTaskServiceImpl implements UserRewardTaskService {
    @Autowired
    private UserRewardTaskDao userRewardTaskDao;
    // 预留MQ发送依赖（如有需要可注入）
    // @Autowired
    // private RewardTaskMqProducer rewardTaskMqProducer;

    @Override
    public void addTask(UserRewardTask task) {
        userRewardTaskDao.insert(task);
        // 预留：如需通过MQ发送任务，可在此扩展
        // rewardTaskMqProducer.send(task);
    }

    @Override
    public void updateTask(UserRewardTask task) {
        userRewardTaskDao.updateById(task);
    }

    @Override
    public UserRewardTask getTaskById(Long id) {
        return userRewardTaskDao.selectById(id);
    }

    @Override
    public List<UserRewardTask> getPendingTasks(int limit) {
        return userRewardTaskDao.selectPendingTasks(limit);
    }

    /**
     * 创建订单返佣任务，支持事务模板传递，保证与业务一致性。
     */
    public void createOrderRewardTask(Integer userId, String orderId, Integer spreadUid, String contextJson, TransactionTemplate transactionTemplate) {
        transactionTemplate.execute(status -> {
            createOrderRewardTask(userId, orderId, spreadUid, contextJson);
            return null;
        });
    }

    /**
     * 创建邀请奖励任务，支持事务模板传递，保证与业务一致性。
     */
    public void createInviteRewardTask(Integer userId, Integer inviteUid, String contextJson, TransactionTemplate transactionTemplate) {
        transactionTemplate.execute(status -> {
            createInviteRewardTask(userId, inviteUid, contextJson);
            return null;
        });
    }

    /**
     * 创建代理奖励任务，支持事务模板传递，保证与业务一致性。
     */
    public void createAgentRewardTask(Integer userId, Integer inviteUid, String contextJson, TransactionTemplate transactionTemplate) {
        transactionTemplate.execute(status -> {
            createAgentRewardTask(userId, inviteUid, contextJson);
            return null;
        });
    }

    /**
     * 创建订单返佣任务（无事务模板，直接写库）
     */
    @Override
    public void createOrderRewardTask(Integer userId, String orderId, Integer spreadUid, String contextJson) {
        UserRewardTask task = new UserRewardTask();
        task.setUserId(userId);
        task.setOrderId(orderId);
        task.setInviteUid(spreadUid);
        task.setTaskType(UserRewardTask.TASK_TYPE_ORDER_REWARD);
        task.setStatus("PENDING");
        task.setContext(contextJson);
        addTask(task);
    }

    /**
     * 创建邀请奖励任务（无事务模板，直接写库）
     */
    @Override
    public void createInviteRewardTask(Integer userId, Integer inviteUid, String contextJson) {
        UserRewardTask task = new UserRewardTask();
        task.setUserId(userId);
        task.setInviteUid(inviteUid);
        task.setTaskType(UserRewardTask.TASK_TYPE_INVITE_REWARD);
        task.setStatus("PENDING");
        task.setContext(contextJson);
        addTask(task);
    }

    /**
     * 创建代理奖励任务（无事务模板，直接写库）
     */
    @Override
    public void createAgentRewardTask(Integer userId, Integer inviteUid, String contextJson) {
        UserRewardTask task = new UserRewardTask();
        task.setUserId(userId);
        task.setInviteUid(inviteUid);
        task.setTaskType(UserRewardTask.TASK_TYPE_AGENT_REWARD);
        task.setStatus("PENDING");
        task.setContext(contextJson);
        addTask(task);
    }
} 