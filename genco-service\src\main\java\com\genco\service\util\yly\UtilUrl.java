package com.genco.service.util.yly;

class UtilUrl {
    public static String baseUrl = "https://open-api.10ss.net/";

    public static String openType = baseUrl + "oauth/authorize";

    public static String freeType = baseUrl + "oauth/oauth";

    public static String addPrinter = baseUrl + "printer/addprinter";

    public static String scanCodeModel = baseUrl + "oauth/scancodemodel";

    public static String printIndex = baseUrl + "print/index";

    public static String picturePrintIndex = baseUrl + "pictureprint/index";

    public static String expressPrintIndex = baseUrl + "expressprint/index";

    public static String printerSetVoice = baseUrl + "printer/setvoice";

    public static String printerDeleteVoice = baseUrl + "printer/deletevoice";

    public static String printerDeletePrinter = baseUrl + "printer/deleteprinter";

    public static String printMenuAddPrintMenu = baseUrl + "printmenu/addprintmenu";

    public static String printShutdownRestart = baseUrl + "printer/shutdownrestart";

    public static String printSetSound = baseUrl + "printer/setsound";

    public static String printPrintInfo = baseUrl + "printer/printinfo";

    public static String printGetVersion = baseUrl + "printer/getversion";

    public static String printCancelAll = baseUrl + "printer/cancelall";

    public static String printCancelOne = baseUrl + "printer/cancelone";

    public static String printSetIcon = baseUrl + "printer/seticon";

    public static String printDeleteIcon = baseUrl + "printer/deleteicon";

    public static String printBtnPrint = baseUrl + "printer/btnprint";

    public static String printGetOrder = baseUrl + "printer/getorder";

    public static String oauthSetPushUrl = baseUrl + "oauth/setpushurl";

    public static String printerGetOrderStatus = baseUrl + "printer/getorderstatus";

    public static String printerGetOrderPagingList = baseUrl + "printer/getorderpaginglist";

    public static String printerGetPrintStatus = baseUrl + "printer/getprintstatus";
}
