{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:51:45.092",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.CrmebAdminApplication",
                    "message": "Starting CrmebAdminApplication on 192.168.1.4 with PID 74809 (/Users/<USER>/code/easyshop/crmeb/crmeb-admin/target/classes started by zhen<PERSON><PERSON> in /Users/<USER>/code/easyshop/crmeb)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:51:45.095",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.CrmebAdminApplication",
                    "message": "No active profile set, falling back to default profiles: default" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:09.292",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Multiple Spring Data modules found, entering strict repository configuration mode!" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:09.338",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Bootstrapping Spring Data Redis repositories in DEFAULT mode." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:09.887",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Finished Spring Data repository scanning in 356ms. Found 0 Redis repository interfaces." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:21.151",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:21.212",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:21.253",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@f613067' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:21.281",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:21.363",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:25.171",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat initialized with port(s): 8082 (http)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:25.272",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Initializing ProtocolHandler ["http-nio-8082"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:25.287",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Starting service [Tomcat]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:25.288",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardEngine",
                    "message": "Starting Servlet engine: [Apache Tomcat/9.0.33]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:27.999",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.jasper.servlet.TldScanner",
                    "message": "At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:28.050",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring embedded WebApplicationContext" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:52:28.064",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Root WebApplicationContext: initialization completed in 42517 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:14.930",
                    "level": "INFO",
                    "thread": "main",
                    "class": "c.g.s.s.impl.PaymentStrategyFactory",
                    "message": "支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:22.714",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.a.e.web.EndpointLinksResolver",
                    "message": "Exposing 2 endpoint(s) beneath base path '/actuator'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:23.397",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.PropertySourcedRequestMappingHandlerMapping",
                    "message": "Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:23.686",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:23.690",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:24.372",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.web.DefaultSecurityFilterChain",
                    "message": "Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4eb48298, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d90764a, org.springframework.security.web.header.HeaderWriterFilter@45451333, org.springframework.web.filter.CorsFilter@46d1b59, org.springframework.web.filter.CorsFilter@46d1b59, org.springframework.web.filter.CorsFilter@46d1b59, org.springframework.security.web.authentication.logout.LogoutFilter@663cf5d7, com.genco.admin.filter.JwtAuthenticationTokenFilter@65e4cb84, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@147375b3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69944a90, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2d3bb944, org.springframework.security.web.session.SessionManagementFilter@3855b27e, org.springframework.security.web.access.ExceptionTranslationFilter@5981f2c6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c4c7e51]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:28.342",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Context refreshed" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:28.466",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Found 2 custom documentation plugin(s)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:28.764",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.207",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.281",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getByIdsUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.335",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.412",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.482",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.493",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.693",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.716",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.875",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:30.893",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.020",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.293",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updatePhoneUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.375",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.602",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.654",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.665",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.693",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.716",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.740",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.764",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:31.828",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.013",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.107",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.166",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.173",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.297",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.418",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.448",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.479",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.495",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.500",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.618",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.707",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.713",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.769",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.804",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:32.888",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.067",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.182",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.254",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.485",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.609",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.663",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.668",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.691",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.735",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.762",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.783",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.792",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.821",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.828",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.843",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.850",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.858",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.942",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.950",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.976",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.989",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:33.995",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.005",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.030",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.036",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.053",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.058",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.077",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.084",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.100",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.121",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.138",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.148",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.174",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.206",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.217",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.238",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.245",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.280",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.305",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListTreeUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.325",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.334",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.340",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.388",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.458",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.475",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.485",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.491",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.506",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.533",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_23" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.542",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.601",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.610",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.623",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.654",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_24" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.660",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.669",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.680",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.699",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.744",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.753",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.757",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.787",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_26" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.809",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.831",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.844",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.876",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_27" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.898",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.907",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.912",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.924",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.940",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.971",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_28" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:34.985",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.013",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.021",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.027",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.044",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.077",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_29" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.091",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_23" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.108",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.114",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.119",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.130",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.143",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_30" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.160",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.166",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.414",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_31" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.431",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_24" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.480",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_23" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.553",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_32" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.568",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_24" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.579",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.584",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.594",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_33" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.600",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.611",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.614",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.647",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.663",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_34" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.678",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: balanceUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.710",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_35" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.718",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.740",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_36" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.750",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_26" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.765",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.770",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_26" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.808",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.811",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.850",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.881",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_37" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.891",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_27" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.897",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_28" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.912",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.919",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_27" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:35.989",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_28" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.000",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.095",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.171",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: webHookUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.182",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: webHookUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.248",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskScheduler",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.277",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Starting ProtocolHandler ["http-nio-8082"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.529",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat started on port(s): 8082 (http) with context path ''" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.547",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.CrmebAdminApplication",
                    "message": "Started CrmebAdminApplication in 121.352 seconds (JVM running for 127.632)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:36.938",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} inited" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:46.561",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring DispatcherServlet 'dispatcherServlet'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:46.563",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Initializing Servlet 'dispatcherServlet'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:46.623",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed initialization in 59 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:46.863",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:47.123",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:51.489",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=123456, key=null, code=null), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@7b11a244]]，cost time：********** ns，cost：3886 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:51.552",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:53:51.553",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:06.210",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:06.219",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:06.810",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.FundsMonitorController.getList(FundsMonitorRequest,PageParamRequest)，prams：[FundsMonitorRequest(keywords=, dateLimit=, title=null, category=, type=, linkId=), PageParamRequest(page=1, limit=20)]，cost time：510666205 ns，cost：510 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:06.872",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:06.873",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:16.168",
                    "level": "INFO",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Shutting down ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 20:54:16.236",
                    "level": "INFO",
                    "thread": "SpringContextShutdownHook",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} closing ..." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:51.749",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.CrmebAdminApplication",
                    "message": "Starting CrmebAdminApplication on 192.168.1.4 with PID 75212 (/Users/<USER>/code/easyshop/crmeb/genco-admin/target/classes started by zhengfeng in /Users/<USER>/code/easyshop/crmeb)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:51.752",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.CrmebAdminApplication",
                    "message": "No active profile set, falling back to default profiles: default" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:55.538",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Multiple Spring Data modules found, entering strict repository configuration mode!" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:55.545",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Bootstrapping Spring Data Redis repositories in DEFAULT mode." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:55.630",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.d.r.c.RepositoryConfigurationDelegate",
                    "message": "Finished Spring Data repository scanning in 37ms. Found 0 Redis repository interfaces." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:58.566",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:58.583",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:58.590",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@50fdf44f' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:58.598",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:58.621",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker",
                    "message": "Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:59.632",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat initialized with port(s): 8082 (http)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:59.665",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Initializing ProtocolHandler ["http-nio-8082"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:59.667",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardService",
                    "message": "Starting service [Tomcat]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:08:59.667",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.catalina.core.StandardEngine",
                    "message": "Starting Servlet engine: [Apache Tomcat/9.0.33]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:00.667",
                    "level": "INFO",
                    "thread": "main",
                    "class": "org.apache.jasper.servlet.TldScanner",
                    "message": "At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:00.684",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring embedded WebApplicationContext" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:00.686",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Root WebApplicationContext: initialization completed in 8796 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:23.547",
                    "level": "INFO",
                    "thread": "main",
                    "class": "c.g.s.s.impl.PaymentStrategyFactory",
                    "message": "支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:28.687",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.a.e.web.EndpointLinksResolver",
                    "message": "Exposing 2 endpoint(s) beneath base path '/actuator'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:29.223",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.PropertySourcedRequestMappingHandlerMapping",
                    "message": "Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:29.352",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:29.354",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Initializing ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:30.133",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.web.DefaultSecurityFilterChain",
                    "message": "Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@637d111d, org.springframework.security.web.context.SecurityContextPersistenceFilter@1253b822, org.springframework.security.web.header.HeaderWriterFilter@683ed81b, org.springframework.web.filter.CorsFilter@819fb19, org.springframework.web.filter.CorsFilter@819fb19, org.springframework.web.filter.CorsFilter@819fb19, org.springframework.security.web.authentication.logout.LogoutFilter@52035328, com.genco.admin.filter.JwtAuthenticationTokenFilter@7afbf2a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3cc2e3e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5807ea46, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a917017, org.springframework.security.web.session.SessionManagementFilter@5bdb6ea8, org.springframework.security.web.access.ExceptionTranslationFilter@62c47480, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5bb39285]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:33.763",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Context refreshed" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:33.884",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.p.DocumentationPluginsBootstrapper",
                    "message": "Found 2 custom documentation plugin(s)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:34.153",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.327",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.392",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getByIdsUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.464",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.543",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.632",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.658",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.894",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:35.920",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.016",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.035",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.129",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.367",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updatePhoneUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.434",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.553",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.584",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.606",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.647",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.656",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.686",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.712",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.766",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:36.945",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.037",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.089",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.093",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.249",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.390",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.584",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.690",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.702",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.707",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.912",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.963",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:37.969",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.057",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.093",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.214",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.395",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.538",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.609",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.775",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.877",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.931",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.934",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.955",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:38.986",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.233",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.268",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_7" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.292",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.341",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.347",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.366",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.372",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_8" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.378",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.482",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.487",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.518",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.523",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_9" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.539",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.548",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.576",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.584",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.605",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.609",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_10" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.626",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.635",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.659",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.682",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.689",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.698",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_11" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.725",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.752",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.768",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.778",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.783",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_12" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.812",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.838",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListTreeUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.858",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.867",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.882",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:39.923",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.001",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.007",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.025",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.030",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.037",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_13" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.066",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_23" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.083",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.091",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.096",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.113",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_14" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.145",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_24" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.152",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.176",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.182",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.210",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.262",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.271",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.286",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.317",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_26" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.342",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.353",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.375",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_15" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.407",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_27" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.431",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.440",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.455",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.459",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.470",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_16" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.499",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_28" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.515",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.526",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.541",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.545",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.552",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_17" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.579",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_29" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.602",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_23" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.611",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.628",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.634",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_3" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.641",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingPOST_2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.661",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_30" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.671",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:40.691",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.033",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_31" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.041",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_24" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.120",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_23" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.209",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_32" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.235",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_24" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.239",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingPOST_6" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.255",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_18" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.264",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_33" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.272",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.289",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.295",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.343",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.374",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_34" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.400",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: balanceUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.456",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_35" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.463",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_19" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.514",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_36" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.522",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_26" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.542",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.549",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_26" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.614",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_20" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.639",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.701",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: deleteUsingGET_21" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.747",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: getListUsingGET_37" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.755",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_27" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.772",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: infoUsingGET_28" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.783",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: saveUsingPOST_22" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.800",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_27" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.804",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateUsingPOST_28" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.808",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: updateStatusUsingGET_4" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.896",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.s.ApiListingReferenceScanner",
                    "message": "Scanning for api listing references" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.973",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: webHookUsingPOST_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:41.977",
                    "level": "INFO",
                    "thread": "main",
                    "class": "s.d.s.w.r.o.CachingOperationNameGenerator",
                    "message": "Generating unique operation named: webHookUsingGET_1" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:42.056",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.s.concurrent.ThreadPoolTaskScheduler",
                    "message": "Initializing ExecutorService" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:42.090",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.apache.coyote.http11.Http11NioProtocol",
                    "message": "Starting ProtocolHandler ["http-nio-8082"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:42.413",
                    "level": "INFO",
                    "thread": "main",
                    "class": "o.s.b.w.embedded.tomcat.TomcatWebServer",
                    "message": "Tomcat started on port(s): 8082 (http) with context path ''" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:42.435",
                    "level": "INFO",
                    "thread": "main",
                    "class": "com.genco.admin.CrmebAdminApplication",
                    "message": "Started CrmebAdminApplication in 52.644 seconds (JVM running for 53.862)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:09:43.069",
                    "level": "INFO",
                    "thread": "crmeb-scheduled-task-pool-2",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} inited" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.003",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "o.a.c.c.C.[Tomcat].[localhost].[/]",
                    "message": "Initializing Spring DispatcherServlet 'dispatcherServlet'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.004",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Initializing Servlet 'dispatcherServlet'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.022",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed initialization in 17 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.169",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.244",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.523",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.FundsMonitorController.getList(FundsMonitorRequest,PageParamRequest)，prams：[FundsMonitorRequest(keywords=, dateLimit=, title=null, category=, type=, linkId=), PageParamRequest(page=1, limit=20)]，cost time：150086711 ns，cost：150 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.578",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:13.579",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-1",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:20.175",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:20.176",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 请求详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:21.298",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "com.genco.admin.acpect.ControllerAspect",
                    "message": "Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=123456, key=null, code=null), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@38d26d0f]]，cost time：********** ns，cost：1106 ms" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:21.301",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:21.302",
                    "level": "INFO",
                    "thread": "http-nio-8082-exec-2",
                    "class": "c.g.c.i.ControllerLogInterceptor",
                    "message": "[DETAIL] 响应详情: {}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:25.714",
                    "level": "INFO",
                    "thread": "SpringContextShutdownHook",
                    "class": "o.s.s.concurrent.ThreadPoolTaskExecutor",
                    "message": "Shutting down ExecutorService 'taskExecutor'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:25.735",
                    "level": "INFO",
                    "thread": "SpringContextShutdownHook",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} closing ..." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-14 21:11:25.749",
                    "level": "INFO",
                    "thread": "SpringContextShutdownHook",
                    "class": "com.alibaba.druid.pool.DruidDataSource",
                    "message": "{dataSource-1} closed" }
                    
