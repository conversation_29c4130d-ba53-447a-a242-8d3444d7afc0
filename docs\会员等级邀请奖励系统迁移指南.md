# 会员等级邀请奖励系统迁移指南

## 概述

本文档描述了如何将GENCO系统从原有的代理/合作伙伴邀请奖励模式迁移到新的会员等级邀请奖励模式。

## 系统架构变更

### 原有模式（代理/合作伙伴模式）
- 用户等级：1-3级为代理，4-6级为合作伙伴
- 奖励配置：基于代理/合作伙伴身份的固定奖励
- 配置复杂：需要维护多个代理和合作伙伴的奖励配置

### 新模式（会员等级模式）
- 用户等级：0-4级会员（0级-普通用户，1级-铜牌，2级-银牌，3级-金牌，4级-钻石）
- 奖励配置：基于邀请人和被邀请人等级的精细化奖励
- 配置灵活：支持不同等级组合的差异化奖励策略

## 核心变更内容

### 1. 数据库变更

#### 会员等级配置表 (eb_system_user_level)
```sql
-- 新增5个会员等级配置
INSERT INTO `eb_system_user_level` VALUES
(1, '普通用户', 0, 0, 100, '', 1, 0, NOW()),
(2, '铜牌会员', 100, 1, 95, '', 1, 0, NOW()),
(3, '银牌会员', 500, 2, 90, '', 1, 0, NOW()),
(4, '金牌会员', 1500, 3, 85, '', 1, 0, NOW()),
(5, '钻石会员', 5000, 4, 80, '', 1, 0, NOW());
```

#### 系统配置表 (eb_system_config)
- 新增会员等级模式开关：`use_member_level_mode`
- 新增会员邀请奖励开关：`member_invite_reward_enable`
- 新增25个一级奖励配置：`member_level_{0-4}_invite_level_{0-4}_reward_1l`
- 新增5个二级奖励配置：`member_level_{0-4}_invite_reward_2l`
- 新增5个三级奖励配置：`member_level_{0-4}_invite_reward_3l`

### 2. 代码变更

#### 新增文件
1. **MemberLevelRewardUtil.java** - 会员等级奖励工具类
2. **SystemMemberLevelController.java** - 会员等级管理控制器
3. **member_level_reward_init.sql** - 数据库初始化脚本

#### 修改文件
1. **Constants.java** - 新增会员等级奖励配置常量
2. **InviteRewardProcessor.java** - 重构为会员等级邀请奖励处理器
3. **AgentRewardProcessor.java** - 兼容会员等级模式
4. **UserServiceImpl.java** - 启用邀请奖励任务创建

### 3. 奖励机制变更

#### 原有机制
```
代理邀请代理 -> 固定奖励
代理邀请合作伙伴 -> 固定奖励
合作伙伴邀请代理 -> 固定奖励
合作伙伴邀请合作伙伴 -> 固定奖励
```

#### 新机制
```
{邀请人等级}邀请{被邀请人等级} -> 差异化奖励
支持三级奖励：
- 一级奖励：直接邀请人获得（基于双方等级）
- 二级奖励：邀请人的上级获得（基于上级等级）
- 三级奖励：邀请人的上级的上级获得（基于上级等级）
```

## 迁移步骤

### 第一步：数据库迁移
```bash
# 执行数据库初始化脚本
mysql -u username -p database_name < docs/sql/member_level_reward_init.sql
```

### 第二步：代码部署
1. 部署新增的Java文件
2. 重启应用服务

### 第三步：系统配置
1. 登录管理后台
2. 访问"系统管理 -> 会员等级管理"
3. 点击"切换到会员等级模式"
4. 配置各等级的邀请奖励金额

### 第四步：验证测试
1. 创建不同等级的测试用户
2. 测试邀请关系绑定
3. 验证奖励发放是否正确
4. 检查账单记录

## 配置说明

### 会员等级配置
| 等级 | 名称 | 经验值要求 | Grade | 折扣 |
|------|------|------------|-------|------|
| 0级  | 普通用户 | 0 | 0 | 100% |
| 1级  | 铜牌会员 | 100 | 1 | 95% |
| 2级  | 银牌会员 | 500 | 2 | 90% |
| 3级  | 金牌会员 | 1500 | 3 | 85% |
| 4级  | 钻石会员 | 5000 | 4 | 80% |

### 奖励配置示例
```
1级会员邀请2级会员 -> 一级奖励：8.00元
1级会员的上级（假设3级） -> 二级奖励：5.00元
1级会员的上级的上级（假设4级） -> 三级奖励：2.00元
```

## 兼容性说明

### 向后兼容
- 原有的代理/合作伙伴模式仍然保留
- 通过配置开关可以在两种模式间切换
- 原有配置常量标记为@Deprecated但仍可使用

### 切换模式
```java
// 切换到会员等级模式
systemConfigService.updateOrSaveValueByKey("use_member_level_mode", "1");

// 切换回代理/合作伙伴模式
systemConfigService.updateOrSaveValueByKey("use_member_level_mode", "0");
```

## 管理接口

### 会员等级管理
- `GET /api/admin/system/member-level/list` - 获取会员等级列表
- `POST /api/admin/system/member-level/save` - 新增会员等级
- `POST /api/admin/system/member-level/update` - 修改会员等级
- `POST /api/admin/system/member-level/delete/{id}` - 删除会员等级

### 奖励配置管理
- `GET /api/admin/system/member-level/reward-config` - 获取奖励配置
- `POST /api/admin/system/member-level/reward-config` - 更新奖励配置
- `POST /api/admin/system/member-level/switch-to-member-mode` - 切换到会员模式
- `POST /api/admin/system/member-level/switch-to-agent-mode` - 切换到代理模式

## 注意事项

### 1. 数据一致性
- 迁移前请备份数据库
- 确保用户等级数据的正确性
- 验证邀请关系链的完整性

### 2. 性能考虑
- 会员等级信息会被缓存，提高查询性能
- 大量用户迁移时建议分批处理
- 监控奖励任务处理性能

### 3. 业务影响
- 迁移期间建议暂停新用户注册
- 通知现有用户等级变更情况
- 准备客服应对用户咨询

### 4. 监控指标
- 邀请奖励发放成功率
- 账单创建成功率
- 用户等级分布情况
- 奖励金额统计

## 回滚方案

如果需要回滚到原有模式：

1. 设置配置开关
```sql
UPDATE eb_system_config SET value = '0' WHERE key = 'use_member_level_mode';
```

2. 恢复原有用户等级（如果需要）
```sql
-- 根据实际情况恢复用户等级
UPDATE eb_user SET level = original_level WHERE condition;
```

3. 重启应用服务

## 技术支持

如遇到问题，请联系技术团队或查看相关日志：
- 应用日志：查看InviteRewardProcessor和AgentRewardProcessor的日志
- 数据库日志：检查账单创建和用户更新操作
- 系统配置：验证相关配置项是否正确设置
