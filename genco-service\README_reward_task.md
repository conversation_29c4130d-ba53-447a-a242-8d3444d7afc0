# 奖励/返现任务调度与处理设计方案

## 1. 设计目标

- 支持订单返现、邀请奖励、代理奖励等多种奖励任务的异步处理。
- 任务落地后可定时调度、自动重试、灵活扩展。
- 返现/奖励金额、链路、类型均可配置，支持多级分润。
- 账单明细与奖励任务强一致，便于财务对账。

---

## 2. 任务表设计

表名：`eb_user_reward_task`

| 字段名         | 类型         | 说明                 |
| -------------- | ------------ | -------------------- |
| id             | BIGINT       | 主键                 |
| user_id        | BIGINT       | 奖励归属用户         |
| order_id       | VARCHAR(64)  | 关联订单号，可为空   |
| invite_uid     | BIGINT       | 邀请人/上级ID，可为空|
| task_type      | VARCHAR(32)  | 任务类型             |
| status         | VARCHAR(16)  | 状态（PENDING等）    |
| context        | TEXT         | 任务上下文JSON       |
| create_time    | DATETIME     | 创建时间             |
| update_time    | DATETIME     | 更新时间             |

---

## 3. 任务落地入口

- 订单同步（如订单状态SETTLED时）：落地订单返现任务
- 用户邀请绑定：落地邀请奖励任务
- 会员/代理购买：落地代理奖励任务

所有任务写入均支持与主业务同事务，保证一致性。

---

## 4. 任务调度与处理

- 定时任务调度器（`UserRewardTaskScheduler`）定期拉取待处理任务。
- 根据`task_type`分发到不同的Processor（如`OrderRewardProcessor`、`InviteRewardProcessor`等）。
- Processor处理成功则任务状态置为`COMPLETED`，失败则抛异常，任务状态置为`FAILED`，下次自动重试。
- 处理器可灵活扩展，支持多种奖励类型。

---

## 5. 返现/奖励处理逻辑（以订单返现为例）

- 查询订单、用户、推广链路（最多三级）。
- 通过`SystemConfigService`+`Constants`获取返现率配置：
    - 平台返现率、普通用户/代理/合作伙伴各级返现率
- 根据用户`level`区分普通用户、代理、合作伙伴，分别计算返现金额：
    - 当前用户：`amount * platformRate * userRate`
    - 一级推广人：`amount * platformRate * user2LRate`
    - 二级推广人：`amount * platformRate * user3LRate`
- 每次返现都写入`UserBill`账单，category区分类型。
- 处理失败抛异常，任务自动重试。

---

## 6. 主要扩展点

- 任务类型、处理器可灵活扩展。
- 返现率、奖励规则均可通过系统配置动态调整。
- 支持MQ异步写入（预留扩展点）。
- 支持失败任务自动重试，便于高可用。

---

## 7. 前端接口建议

- 提供奖励任务列表、详情查询接口，便于用户/运营查看奖励进度。
- 支持分页、筛选、状态查询。

---

## 8. 典型调用样例

```java
transactionTemplate.execute(status -> {
    // 1. 主业务写入
    // 2. 落地奖励任务
    userRewardTaskService.createOrderRewardTask(userId, orderId, spreadUid, contextJson);
    return true;
});
```

---

## 9. 返现率配置常量

- `PLATFORM_CASH_BACK_RATE` 平台返现率
- `NORMAL_CASH_BACK_RATE` 普通用户返现率
- `NORMAL_CASH_BACK_RATE_2L` 普通用户二级返现率
- `NORMAL_CASH_BACK_RATE_3L` 普通用户三级返现率
- `AGENT_CASH_BACK_RATE` 代理返现率
- `AGENT_CASH_BACK_RATE_2L` 代理二级返现率
- `AGENT_CASH_BACK_RATE_3L` 代理三级返现率
- `PARTNER_CASH_BACK_RATE` 合作伙伴返现率
- `PARTNER_CASH_BACK_RATE_2L` 合作伙伴二级返现率
- `PARTNER_CASH_BACK_RATE_3L` 合作伙伴三级返现率

---

## 10. 其他说明

- 账单明细与奖励任务强一致，便于财务对账。
- 失败任务可通过后台管理界面人工干预或重试。
- 代码结构清晰，便于团队后续扩展和维护。 