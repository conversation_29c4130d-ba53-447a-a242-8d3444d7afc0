package com.genco.admin.controller.system;

import com.genco.common.constants.Constants;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.response.CommonResult;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.SystemUserLevelService;
import com.genco.service.utils.MemberLevelRewardUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员等级管理控制器
 * 用于管理会员等级配置和邀请奖励设置
 */
@Slf4j
@RestController
@RequestMapping("api/admin/system/member-level")
@Api(tags = "会员等级管理")
@Validated
public class SystemMemberLevelController {

    @Autowired
    private SystemUserLevelService systemUserLevelService;
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    @Autowired
    private MemberLevelRewardUtil memberLevelRewardUtil;

    @PreAuthorize("hasAuthority('admin:system:member-level:list')")
    @ApiOperation(value = "获取会员等级列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public CommonResult<List<SystemUserLevel>> getList() {
        List<SystemUserLevel> list = systemUserLevelService.getUsableList();
        return CommonResult.success(list);
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:info')")
    @ApiOperation(value = "获取会员等级详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    public CommonResult<SystemUserLevel> info(@ApiParam(value = "等级ID", required = true) @PathVariable Integer id) {
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(id);
        return CommonResult.success(userLevel);
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:save')")
    @ApiOperation(value = "新增会员等级")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public CommonResult<String> save(@RequestBody @Validated SystemUserLevel systemUserLevel) {
        if (systemUserLevelService.save(systemUserLevel)) {
            // 清除缓存
            memberLevelRewardUtil.clearLevelCache();
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:update')")
    @ApiOperation(value = "修改会员等级")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public CommonResult<String> update(@RequestBody @Validated SystemUserLevel systemUserLevel) {
        if (systemUserLevelService.updateById(systemUserLevel)) {
            // 刷新缓存
            memberLevelRewardUtil.refreshLevelCache(systemUserLevel.getId());
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:delete')")
    @ApiOperation(value = "删除会员等级")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    public CommonResult<String> delete(@ApiParam(value = "等级ID", required = true) @PathVariable Integer id) {
        if (systemUserLevelService.removeById(id)) {
            // 清除缓存
            memberLevelRewardUtil.clearLevelCache();
            return CommonResult.success();
        } else {
            return CommonResult.failed();
        }
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:reward-config')")
    @ApiOperation(value = "获取会员邀请奖励配置")
    @RequestMapping(value = "/reward-config", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getRewardConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // 获取邀请奖励开关
        String enableConfig = systemConfigService.getValueByKey(Constants.MEMBER_INVITE_REWARD_ENABLE);
        config.put("enable", "1".equals(enableConfig));
        
        // 获取会员等级模式开关
        String memberModeConfig = systemConfigService.getValueByKey("use_member_level_mode");
        config.put("useMemberLevelMode", "1".equals(memberModeConfig));
        
        // 获取各等级邀请奖励配置
        Map<String, Map<String, String>> rewardMatrix = new HashMap<>();
        for (int inviterGrade = 0; inviterGrade <= 4; inviterGrade++) {
            Map<String, String> inviterConfig = new HashMap<>();
            
            // 一级奖励（根据被邀请人等级）
            Map<String, String> firstLevelRewards = new HashMap<>();
            for (int inviteeGrade = 0; inviteeGrade <= 4; inviteeGrade++) {
                String key = memberLevelRewardUtil.getFirstLevelRewardKey(inviterGrade, inviteeGrade);
                String value = systemConfigService.getValueByKey(key);
                firstLevelRewards.put("level_" + inviteeGrade, value != null ? value : "0");
            }
            inviterConfig.put("firstLevel", firstLevelRewards.toString());
            
            // 二级奖励
            String secondKey = memberLevelRewardUtil.getSecondLevelRewardKey(inviterGrade);
            String secondValue = systemConfigService.getValueByKey(secondKey);
            inviterConfig.put("secondLevel", secondValue != null ? secondValue : "0");
            
            // 三级奖励
            String thirdKey = memberLevelRewardUtil.getThirdLevelRewardKey(inviterGrade);
            String thirdValue = systemConfigService.getValueByKey(thirdKey);
            inviterConfig.put("thirdLevel", thirdValue != null ? thirdValue : "0");
            
            rewardMatrix.put("grade_" + inviterGrade, inviterConfig);
        }
        
        config.put("rewardMatrix", rewardMatrix);
        return CommonResult.success(config);
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:reward-config')")
    @ApiOperation(value = "更新会员邀请奖励配置")
    @RequestMapping(value = "/reward-config", method = RequestMethod.POST)
    public CommonResult<String> updateRewardConfig(@RequestBody Map<String, Object> configData) {
        try {
            // 更新邀请奖励开关
            Boolean enable = (Boolean) configData.get("enable");
            systemConfigService.updateOrSaveValueByKey(Constants.MEMBER_INVITE_REWARD_ENABLE, enable ? "1" : "0");
            
            // 更新会员等级模式开关
            Boolean useMemberLevelMode = (Boolean) configData.get("useMemberLevelMode");
            systemConfigService.updateOrSaveValueByKey("use_member_level_mode", useMemberLevelMode ? "1" : "0");
            
            // 更新奖励配置矩阵
            @SuppressWarnings("unchecked")
            Map<String, Map<String, Object>> rewardMatrix = (Map<String, Map<String, Object>>) configData.get("rewardMatrix");
            
            if (rewardMatrix != null) {
                for (int inviterGrade = 0; inviterGrade <= 4; inviterGrade++) {
                    String gradeKey = "grade_" + inviterGrade;
                    Map<String, Object> gradeConfig = rewardMatrix.get(gradeKey);
                    
                    if (gradeConfig != null) {
                        // 更新一级奖励配置
                        @SuppressWarnings("unchecked")
                        Map<String, String> firstLevelRewards = (Map<String, String>) gradeConfig.get("firstLevel");
                        if (firstLevelRewards != null) {
                            for (int inviteeGrade = 0; inviteeGrade <= 4; inviteeGrade++) {
                                String configKey = memberLevelRewardUtil.getFirstLevelRewardKey(inviterGrade, inviteeGrade);
                                String configValue = firstLevelRewards.get("level_" + inviteeGrade);
                                if (configValue != null) {
                                    systemConfigService.updateOrSaveValueByKey(configKey, configValue);
                                }
                            }
                        }
                        
                        // 更新二级奖励配置
                        String secondLevel = (String) gradeConfig.get("secondLevel");
                        if (secondLevel != null) {
                            String configKey = memberLevelRewardUtil.getSecondLevelRewardKey(inviterGrade);
                            systemConfigService.updateOrSaveValueByKey(configKey, secondLevel);
                        }
                        
                        // 更新三级奖励配置
                        String thirdLevel = (String) gradeConfig.get("thirdLevel");
                        if (thirdLevel != null) {
                            String configKey = memberLevelRewardUtil.getThirdLevelRewardKey(inviterGrade);
                            systemConfigService.updateOrSaveValueByKey(configKey, thirdLevel);
                        }
                    }
                }
            }
            
            return CommonResult.success("会员邀请奖励配置更新成功");
        } catch (Exception e) {
            log.error("更新会员邀请奖励配置失败", e);
            return CommonResult.failed("更新失败：" + e.getMessage());
        }
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:reward-config')")
    @ApiOperation(value = "切换到会员等级模式")
    @RequestMapping(value = "/switch-to-member-mode", method = RequestMethod.POST)
    public CommonResult<String> switchToMemberMode() {
        // 启用会员等级模式
        systemConfigService.updateOrSaveValueByKey("use_member_level_mode", "1");
        // 启用会员邀请奖励
        systemConfigService.updateOrSaveValueByKey(Constants.MEMBER_INVITE_REWARD_ENABLE, "1");
        
        log.info("系统已切换到会员等级模式");
        return CommonResult.success("已切换到会员等级模式");
    }

    @PreAuthorize("hasAuthority('admin:system:member-level:reward-config')")
    @ApiOperation(value = "切换到代理/合作伙伴模式")
    @RequestMapping(value = "/switch-to-agent-mode", method = RequestMethod.POST)
    public CommonResult<String> switchToAgentMode() {
        // 禁用会员等级模式
        systemConfigService.updateOrSaveValueByKey("use_member_level_mode", "0");
        
        log.info("系统已切换到代理/合作伙伴模式");
        return CommonResult.success("已切换到代理/合作伙伴模式");
    }

    @ApiOperation(value = "获取会员等级统计信息")
    @RequestMapping(value = "/statistics", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        
        // 获取各等级用户数量统计
        List<SystemUserLevel> levels = systemUserLevelService.getUsableList();
        Map<String, Integer> levelCounts = new HashMap<>();
        
        for (SystemUserLevel level : levels) {
            // 这里可以添加统计逻辑，查询每个等级的用户数量
            // Integer count = userService.countByLevel(level.getId());
            // levelCounts.put(level.getName(), count);
            levelCounts.put(level.getName(), 0); // 临时设置为0
        }
        
        statistics.put("levelCounts", levelCounts);
        statistics.put("totalLevels", levels.size());
        
        return CommonResult.success(statistics);
    }
}
