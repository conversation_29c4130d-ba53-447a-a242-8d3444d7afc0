-- 给 eb_user_recharge 表添加外部支付单号字段
ALTER TABLE `eb_user_recharge` 
ADD COLUMN `out_trade_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '外部支付单号' AFTER `refund_price`;

-- 给 eb_user_recharge 表添加支付渠道字段
ALTER TABLE `eb_user_recharge` 
ADD COLUMN `pay_channel` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付渠道' AFTER `out_trade_no`;

-- 添加索引以提高查询性能
ALTER TABLE `eb_user_recharge` 
ADD INDEX `idx_out_trade_no` (`out_trade_no`) USING BTREE;

-- 添加支付渠道索引
ALTER TABLE `eb_user_recharge` 
ADD INDEX `idx_pay_channel` (`pay_channel`) USING BTREE; 