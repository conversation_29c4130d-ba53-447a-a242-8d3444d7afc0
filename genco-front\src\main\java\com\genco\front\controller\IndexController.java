package com.genco.front.controller;


import com.genco.common.model.system.SystemConfig;
import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.CommonResult;
import com.genco.common.response.IndexInfoResponse;
import com.genco.common.response.IndexProductResponse;
import com.genco.front.service.IndexService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户 -- 用户中心
 */
@Slf4j
@RestController("IndexController")
@RequestMapping("api/front")
@Api(tags = "首页")
public class IndexController {

    @Autowired
    private IndexService indexService;

    /**
     * 首页数据
     */
    @ApiOperation(value = "首页数据")
    @RequestMapping(value = "/index", method = RequestMethod.GET)
    public CommonResult<IndexInfoResponse> getIndexInfo() {
        return CommonResult.success(indexService.getIndexInfo());
    }

    /**
     * 首页商品列表
     */
    @ApiOperation(value = "首页商品列表")
    @RequestMapping(value = "/index/product/{type}", method = RequestMethod.GET)
    @ApiImplicitParam(name = "type", value = "类型 【1 精品推荐 2 热门榜单 3首发新品 4促销单品】", dataType = "int", required = true)
    public CommonResult<CommonPage<IndexProductResponse>> getProductList(@PathVariable(value = "type") Integer type, PageParamRequest pageParamRequest) {

        return CommonResult.success(indexService.findIndexProductList(type, pageParamRequest));
    }

    /**
     * 首页品牌商品
     * 排序字段（0：默认；1:价格；2:销量；3：新品；4：返现率）
     */
    @ApiOperation(value = "首页品牌商品")
    @RequestMapping(value = "/index/brand", method = RequestMethod.GET)
    public CommonResult<CommonPage<IndexProductResponse>> searchProductList(@RequestParam String code,
                                                                            @RequestParam Integer orderBy,
                                                                            @RequestParam Boolean isAsc,
                                                                            PageParamRequest pageParamRequest) {
        return CommonResult.success(indexService.findBrandProductList(code, orderBy, isAsc, pageParamRequest));
    }


    /**
     * 热门搜索
     */
    @ApiOperation(value = "热门搜索")
    @RequestMapping(value = "/search/keyword", method = RequestMethod.GET)
    public CommonResult<List<HashMap<String, Object>>> hotKeywords() {
        return CommonResult.success(indexService.hotKeywords());
    }

    /**
     * 分享配置
     */
    @ApiOperation(value = "分享配置")
    @RequestMapping(value = "/share", method = RequestMethod.GET)
    public CommonResult<HashMap<String, String>> share() {
        return CommonResult.success(indexService.getShareConfig());
    }

    /**
     * 获取会员充值配置
     */
    @ApiOperation(value = "获取会员充值配置")
    @RequestMapping(value = "/index/recharge/config", method = RequestMethod.GET)
    public CommonResult<HashMap<String, String>> getRechargeConfig() {
        return CommonResult.success(indexService.getVipRechargeConfig());
    }


    /**
     * 颜色配置
     */
    @ApiOperation(value = "颜色配置")
    @RequestMapping(value = "/index/color/config", method = RequestMethod.GET)
    public CommonResult<SystemConfig> getColorConfig() {
        return CommonResult.success(indexService.getColorConfig());
    }

    /**
     * 版本信息
     */
    @ApiOperation(value = "获取版本信息")
    @RequestMapping(value = "/index/version", method = RequestMethod.GET)
    public CommonResult<Map<String, Object>> getVersion(@RequestParam String platform) {
        return CommonResult.success(indexService.getVersion(platform));
    }

    /**
     * 全局本地图片域名
     */
    @ApiOperation(value = "全局本地图片域名")
    @RequestMapping(value = "/image/domain", method = RequestMethod.GET)
    public CommonResult<String> getImageDomain() {
        return CommonResult.success(indexService.getImageDomain(), "成功");
    }
}



