package com.genco.service.service;

import com.genco.common.model.product.StoreProductShareRecord;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreProductShareRecordSearchRequest;
import com.github.pagehelper.PageInfo;

public interface StoreProductShareRecordService {
    /**
     * 异步保存商品分享转链记录
     * @param record 记录实体
     */
    void saveAsync(StoreProductShareRecord record);

    /**
     * 同步保存商品分享转链记录
     * @param record 记录实体
     */
    void save(StoreProductShareRecord record);

    /**
     * 分页查询商品分享转链记录（支持多条件搜索）
     * @param request 查询参数（用户ID、昵称、商品ID、商品名、渠道、时间区间等）
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    PageInfo<StoreProductShareRecord> getAdminList(StoreProductShareRecordSearchRequest request, PageParamRequest pageParamRequest);
} 