version: '3.8'
services:
  genco-front:
    build:
      context: ./genco-front
      dockerfile: Dockerfile
    image: genco-front:latest
    container_name: genco-front
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    restart: always

  genco-admin:
    build:
      context: ./genco-admin
      dockerfile: Dockerfile
    image: genco-admin:latest
    container_name: genco-admin
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
    restart: always 