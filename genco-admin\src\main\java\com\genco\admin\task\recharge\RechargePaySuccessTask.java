package com.genco.admin.task.recharge;

import com.genco.common.utils.DateUtil;
import com.genco.service.service.RechargeTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

/**
 * 充值支付成功后置task任务
 */
@Component
@Configuration //读取配置
@EnableScheduling // 2.开启定时任务
public class RechargePaySuccessTask {

    //日志
    private static final Logger logger = LoggerFactory.getLogger(RechargePaySuccessTask.class);

    @Autowired
    private RechargeTaskService rechargeTaskService;

    //    @Scheduled(fixedDelay = 1000 * 60L) //1分钟同步一次数据
    public void init() {
        logger.info("---RechargePaySuccessTask task------produce Data with fixed rate task: Execution Time - {}", DateUtil.nowDateTime());
        try {
            rechargeTaskService.rechargePaySuccessAfter();
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("RechargePaySuccessTask.task" + " | msg : " + e.getMessage());
        }

    }

} 