package com.genco.common.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.List;

/**
 * 生成订单Vo对象
 */
@Data
public class ShopOrderDetailAddVo {
    /**
     * 商品详情数组
     */
    @TableField(value = "product_infos")
    private List<ShopOrderProductInfoAddVo> productInfos;

    /**
     * 支付详情数组
     */
    @TableField(value = "pay_info")
    private ShopOrderPayInfoAddVo payInfo;

    /**
     * 价格详情数组
     */
    @TableField(value = "price_info")
    private ShopOrderPriceInfoVo priceInfo;
}
