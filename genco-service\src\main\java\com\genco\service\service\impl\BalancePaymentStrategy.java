package com.genco.service.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.ObjectUtil;
import com.genco.common.constants.PayConstants;
import com.genco.common.constants.TaskConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.combination.StoreCombination;
import com.genco.common.model.combination.StorePink;
import com.genco.common.model.finance.UserRecharge;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.model.user.User;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.RedisUtil;
import com.genco.common.vo.PaymentRequestVo;
import com.genco.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;

/**
 * 余额支付策略实现
 */
@Slf4j
@Service
public class BalancePaymentStrategy implements PaymentStrategy {

    @Autowired
    private UserService userService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserRechargeService userRechargeService;

    @Autowired
    private StorePinkService storePinkService;

    @Autowired
    private StoreCombinationService storeCombinationService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest) {
        // 用户余额扣除
        User user = userService.getById(paymentRequest.getUid());
        if (ObjectUtil.isNull(user)) throw new CrmebException("用户不存在");
        if (user.getNowMoney().compareTo(paymentRequest.getAmount()) < 0) {
            throw new CrmebException("用户余额不足");
        }

        Boolean execute = transactionTemplate.execute(e -> {
            if ("order".equals(paymentRequest.getBizType())) {
                // 订单支付处理
                StoreOrder storeOrder = storeOrderService.getByOderId(paymentRequest.getOrderNo());
                if (ObjectUtil.isNull(storeOrder)) {
                    throw new CrmebException("订单不存在");
                }

                if (user.getIntegral() < storeOrder.getUseIntegral()) {
                    throw new CrmebException("用户积分不足");
                }

                storeOrder.setPaid(true);
                storeOrder.setPayTime(DateUtil.nowDateTime());
                storeOrderService.updateById(storeOrder);

                // 这里只扣除金额，账单记录在task中处理
                userService.updateNowMoney(user, paymentRequest.getAmount(), "sub");

                // 扣除积分
                if (storeOrder.getUseIntegral() > 0) {
                    userService.updateIntegral(user, storeOrder.getUseIntegral(), "sub");
                }

                // 添加支付成功redis队列
                redisUtil.lPush(TaskConstants.ORDER_TASK_PAY_SUCCESS_AFTER, storeOrder.getOrderId());

                // 处理拼团
                if (storeOrder.getCombinationId() > 0) {
                    handleCombinationOrder(storeOrder, user);
                }

            } else if ("recharge".equals(paymentRequest.getBizType())) {
                // 充值支付处理
                UserRecharge userRecharge = userRechargeService.getInfoByEntity(
                        new UserRecharge().setOrderId(paymentRequest.getOrderNo())
                );
                if (ObjectUtil.isNull(userRecharge)) {
                    throw new CrmebException("充值订单不存在");
                }

                userRecharge.setPaid(true);
                userRecharge.setPayTime(DateUtil.nowDateTime());
                userRechargeService.updateById(userRecharge);

                // 扣除余额
                userService.updateNowMoney(user, paymentRequest.getAmount(), "sub");

                // 添加充值支付成功redis队列
                redisUtil.lPush(TaskConstants.RECHARGE_TASK_PAY_SUCCESS_AFTER, userRecharge.getOrderId());
            }

            return Boolean.TRUE;
        });

        if (!execute) throw new CrmebException("余额支付失败");

        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(paymentRequest.getOrderNo());
        response.setPayType(paymentRequest.getPayType());
        response.setPayChannel(PayConstants.PAY_TYPE_YUE);
        response.setStatus(execute);
        response.setAmount(paymentRequest.getAmount());
        response.setOutTradeNo(paymentRequest.getOrderNo());

        return response;
    }

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        log.info("余额支付查询支付结果开始，订单号：{}", orderNo);

        try {
            // 根据订单号前缀判断业务类型
            String bizType = "unknown";
            if (orderNo.startsWith("wxNo") || orderNo.startsWith("order")) {
                bizType = PayConstants.BIZ_TYPE_ORDER;
            } else if (orderNo.startsWith("recharge")) {
                bizType = PayConstants.BIZ_TYPE_RECHARGE;
            }

            // 构建返回结果
            PaymentQueryResultResponse result = new PaymentQueryResultResponse();
            result.setOrderNo(orderNo);
            result.setOutTradeNo(orderNo);
            result.setPayChannel(PayConstants.PAY_TYPE_YUE);
            result.setBizType(bizType);

            // 余额支付是即时支付，如果订单存在且已支付，则返回支付成功
            if (PayConstants.BIZ_TYPE_ORDER.equals(bizType)) {
                StoreOrder storeOrder = storeOrderService.getByOderId(orderNo);
                if (ObjectUtil.isNotNull(storeOrder) && storeOrder.getPaid()) {
                    result.setPaid(true);
                    result.setAmount(storeOrder.getPayPrice());
                } else {
                    result.setPaid(false);
                    result.setAmount(BigDecimal.ZERO);
                }
            } else if (PayConstants.BIZ_TYPE_RECHARGE.equals(bizType)) {
                UserRecharge userRecharge = userRechargeService.getInfoByEntity(
                        new UserRecharge().setOrderId(orderNo)
                );
                if (ObjectUtil.isNotNull(userRecharge) && userRecharge.getPaid()) {
                    result.setPaid(true);
                    result.setAmount(userRecharge.getPrice());
                } else {
                    result.setPaid(false);
                    result.setAmount(BigDecimal.ZERO);
                }
            } else {
                result.setPaid(false);
                result.setAmount(BigDecimal.ZERO);
            }

            log.info("余额支付查询支付结果成功，订单号：{}，支付状态：{}", orderNo, result.getPaid());
            return result;

        } catch (Exception e) {
            log.error("余额支付查询支付结果异常，订单号：{}", orderNo, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }

    /**
     * 处理拼团订单
     */
    private void handleCombinationOrder(StoreOrder storeOrder, User user) {
        // 判断拼团团长是否存在
        StorePink headPink = new StorePink();
        Integer pinkId = storeOrder.getPinkId();
        if (pinkId > 0) {
            headPink = storePinkService.getById(pinkId);
            if (ObjectUtil.isNull(headPink) || headPink.getIsRefund().equals(true) || headPink.getStatus() == 3) {
                pinkId = 0;
            }
        }
        StoreCombination storeCombination = storeCombinationService.getById(storeOrder.getCombinationId());
        // 如果拼团人数已满，重新开团
        if (pinkId > 0) {
            Integer count = storePinkService.getCountByKid(pinkId);
            if (count >= storeCombination.getPeople()) {
                pinkId = 0;
            }
        }
        // 生成拼团表数据
        StorePink storePink = new StorePink();
        storePink.setUid(user.getUid());
        storePink.setAvatar(user.getAvatar());
        storePink.setNickname(user.getNickname());
        storePink.setOrderId(storeOrder.getOrderId());
        storePink.setOrderIdKey(storeOrder.getId());
        storePink.setTotalNum(storeOrder.getTotalNum());
        storePink.setTotalPrice(storeOrder.getTotalPrice());
        storePink.setCid(storeCombination.getId());
        storePink.setPid(storeCombination.getProductId());
        storePink.setPeople(storeCombination.getPeople());
        storePink.setPrice(storeCombination.getPrice());
        Integer effectiveTime = storeCombination.getEffectiveTime();// 有效小时数
        DateTime dateTime = cn.hutool.core.date.DateUtil.date();
        storePink.setAddTime(dateTime.getTime());
        if (pinkId > 0) {
            storePink.setStopTime(headPink.getStopTime());
        } else {
            DateTime hourTime = cn.hutool.core.date.DateUtil.offsetHour(dateTime, effectiveTime);
            long stopTime = hourTime.getTime();
            if (stopTime > storeCombination.getStopTime()) {
                stopTime = storeCombination.getStopTime();
            }
            storePink.setStopTime(stopTime);
        }
        storePink.setKId(pinkId);
        storePink.setIsTpl(false);
        storePink.setIsRefund(false);
        storePink.setStatus(1);
        storePinkService.save(storePink);
        // 如果是开团，需要更新订单数据
        storeOrder.setPinkId(storePink.getId());
        storeOrderService.updateById(storeOrder);
    }

    @Override
    public String getPayChannel() {
        return PayConstants.PAY_TYPE_YUE;
    }
} 