# TikTok 订单同步主键冲突问题修复

## 问题描述

在 TikTok 订单同步过程中，出现以下错误：

```
Duplicate entry '12-579546563097364147' for key 'eb_store_order_info.oid'
```

## 问题分析

1. **根本原因**：`eb_store_order_info` 表有一个唯一索引 `oid`，由 `order_id` 和 `unique` 两个字段组成
2. **冲突原因**：同一个订单的多个 SKU 使用了相同的 `unique` 值（订单号），导致唯一索引冲突
3. **影响范围**：`TiktokOrderSyncServiceImpl`、`OrderTaskServiceImpl`、`TikTokUtil` 中的订单详情创建逻辑

## 解决方案

### 1. 修复 `unique` 字段生成逻辑

将原来使用订单号作为 `unique` 值的方式，改为使用组合值：

```java
// 修复前
storeOrderInfo.setUnique(orderNo);

// 修复后
String uniqueValue = orderNo + "-" + sku.getProductId() + "-" + sku.getSkuId();
storeOrderInfo.setUnique(uniqueValue);
```

### 2. 添加重复检查机制

在批量插入前检查订单详情是否已存在：

```java
// 检查订单详情是否已存在，避免重复插入
List<StoreOrderInfo> existingOrderInfos = storeOrderInfoService.getListByOrderNo(storeOrder.getOrderId());
if (existingOrderInfos != null && !existingOrderInfos.isEmpty()) {
    logger.warn("订单详情已存在，跳过插入，orderId: {}", storeOrder.getOrderId());
    continue;
}
```

### 3. 增强错误处理

添加 try-catch 包装批量插入，提供降级处理：

```java
try {
    storeOrderInfoService.saveBatch(orderInfos);
} catch (Exception e) {
    logger.error("批量插入订单详情失败，orderId: {}, error: {}", storeOrder.getOrderId(), e.getMessage());
    // 如果批量插入失败，尝试逐个插入
    for (StoreOrderInfo orderInfo : orderInfos) {
        try {
            storeOrderInfoService.save(orderInfo);
        } catch (Exception ex) {
            logger.error("单个订单详情插入失败，orderId: {}, unique: {}, error: {}", 
                orderInfo.getOrderId(), orderInfo.getUnique(), ex.getMessage());
        }
    }
}
```

## 修复的文件

1. `genco/genco-service/src/main/java/com/genco/service/service/impl/TiktokOrderSyncServiceImpl.java`
2. `genco/genco-service/src/main/java/com/genco/service/service/impl/OrderTaskServiceImpl.java`
3. `genco/genco-common/src/main/java/com/genco/common/utils/TikTokUtil.java`

## 验证方法

1. 重新部署应用
2. 执行 TikTok 订单同步任务
3. 检查日志中是否还有主键冲突错误
4. 验证数据库中订单详情数据是否正确插入

## 注意事项

1. 修复后的 `unique` 值格式为：`订单号-商品ID-SKU ID`
2. 确保 TikTok API 返回的 `sku.getSkuId()` 不为空
3. 如果 `sku.getSkuId()` 为空，可以考虑使用其他唯一标识符
4. 建议在生产环境部署前进行充分测试 