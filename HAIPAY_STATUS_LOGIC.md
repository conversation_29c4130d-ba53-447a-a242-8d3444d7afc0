# HaiPay状态逻辑说明

## 概述

HaiPay API返回的数据中有两个不同的状态字段，需要正确区分：

1. **API响应状态** - 表示API调用是否成功
2. **支付状态** - 表示具体的支付结果

## 状态字段说明

### 1. API响应状态 (`responseVo.getStatus()`)

这个字段表示HaiPay API调用是否成功：

- `"1"` - API调用成功
- `"0"` - API调用失败

**使用场景**：检查API请求是否成功发送并收到响应

### 2. 支付状态 (`responseVo.getData().getStatus()`)

这个字段表示具体的支付结果：

- `"0"` - 未开始
- `"1"` - 支付中
- `"2"` - 支付成功（终态）
- `"3"` - 支付失败（终态）
- `"-1"` - 异常待确认

**使用场景**：判断订单的实际支付状态

## 代码逻辑

### 正确的处理流程

```java
// 1. 首先检查API调用是否成功
if (!PayConstants.API_STATUS_SUCCESS.equals(responseVo.getStatus())) {
    throw new CrmebException("HaiPay API调用失败: " + responseVo.getMsg());
}

// 2. API调用成功后，再检查支付状态
if (PayConstants.PAY_STATUS_SUCCESS.equals(responseVo.getData().getStatus())) {
    // 支付成功
    result.setPaid(true);
} else {
    // 支付未成功（可能是未开始、支付中、失败等）
    result.setPaid(false);
}
```

### 错误的处理方式

```java
// ❌ 错误：直接检查API响应状态作为支付状态
if (!PayConstants.PAY_STATUS_SUCCESS.equals(responseVo.getStatus())) {
    throw new CrmebException("支付失败");
}
```

这样会导致：
- 即使支付成功，如果API响应状态不是"2"，也会抛出异常
- 混淆了API调用成功和支付成功的概念

## 常量定义

```java
// API响应状态
public static final String API_STATUS_SUCCESS = "1"; // API调用成功
public static final String API_STATUS_FAILED = "0";  // API调用失败

// 支付状态
public static final String PAY_STATUS_PENDING = "0";     // 未开始
public static final String PAY_STATUS_PROCESSING = "1";  // 支付中
public static final String PAY_STATUS_SUCCESS = "2";     // 支付成功
public static final String PAY_STATUS_FAILED = "3";      // 支付失败
public static final String PAY_STATUS_ERROR_ING = "-1";  // 异常待确认
```

## 实际应用场景

### 1. 支付创建

```java
// 创建支付订单时，只需要检查API调用是否成功
HaiPayResponseVo response = haiPayService.createPayment(request);
if (!PayConstants.API_STATUS_SUCCESS.equals(response.getStatus())) {
    throw new CrmebException("支付创建失败");
}
// API调用成功，说明支付订单创建成功
```

### 2. 支付查询

```java
// 查询支付结果时，需要两步检查
HaiPayQueryResponseVo response = haiPayService.queryPayment(request);

// 第一步：检查API调用是否成功
if (!PayConstants.API_STATUS_SUCCESS.equals(response.getStatus())) {
    throw new CrmebException("查询API调用失败");
}

// 第二步：检查支付状态
if (PayConstants.PAY_STATUS_SUCCESS.equals(response.getData().getStatus())) {
    result.setPaid(true);  // 支付成功
} else {
    result.setPaid(false); // 支付未成功
}
```

## 注意事项

1. **API调用成功 ≠ 支付成功**：API调用成功只是说明请求被正确处理，不代表支付已经完成
2. **查询接口的特殊性**：查询接口即使支付失败，API调用也可能是成功的
3. **状态值含义**：不同接口的状态值含义可能不同，需要仔细查看文档
4. **异常处理**：API调用失败时应该抛出异常，支付失败时应该返回相应的状态

## 总结

正确理解和使用这两个状态字段是HaiPay集成成功的关键：

- **API响应状态**：用于判断API调用是否成功
- **支付状态**：用于判断具体的支付结果

只有API调用成功的情况下，才能进一步判断支付状态。这样的逻辑确保了系统的稳定性和准确性。 