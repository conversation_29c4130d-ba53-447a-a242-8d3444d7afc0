package com.genco.front.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.github.pagehelper.util.StringUtil;
import com.genco.common.constants.Constants;
import com.genco.common.constants.SmsConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserToken;
import com.genco.common.request.LoginMobileRequest;
import com.genco.common.request.LoginRequest;
import com.genco.common.request.RegisterThirdUserRequest;
import com.genco.common.response.LoginResponse;
import com.genco.common.token.FrontTokenComponent;
import com.genco.common.token.TikTokOauthToken;
import com.genco.common.utils.CrmebUtil;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.RedisUtil;
import com.genco.front.service.LoginService;
import com.genco.service.service.TikTokNewService;
import com.genco.service.service.UserService;
import com.genco.service.service.UserTokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.Optional;

/**
 * 移动端登录服务类
 */
@Service
public class LoginServiceImpl implements LoginService {

    private static final Logger logger = LoggerFactory.getLogger(LoginServiceImpl.class);

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FrontTokenComponent tokenComponent;

    @Autowired
    UserTokenService userTokenService;

    @Autowired
    TikTokNewService tikTokNewService;

    /**
     * 账号密码登录
     *
     * @return LoginResponse
     */
    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        User user = userService.getByPhone(loginRequest.getPhone());
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("此账号未注册");
        }
        if (!user.getStatus()) {
            throw new CrmebException("此账号被禁用");
        }

        // 校验密码
        String password = CrmebUtil.encryptPassword(loginRequest.getPassword(), loginRequest.getPhone());
        if (!user.getPwd().equals(password)) {
            throw new CrmebException("密码错误");
        }

        LoginResponse loginResponse = new LoginResponse();
        String token = tokenComponent.createToken(user);
        loginResponse.setToken(token);

        //绑定推广关系
        if (loginRequest.getSpreadPid() > 0) {
            bindSpread(user, loginRequest.getSpreadPid());
        }

        // 记录最后一次登录时间
        user.setLastLoginTime(DateUtil.nowDateTime());
        userService.updateById(user);

        loginResponse.setUid(user.getUid());
        loginResponse.setNikeName(user.getNickname());
        loginResponse.setPhone(user.getPhone());
        loginResponse.setIsNewUser(StringUtil.isEmpty(user.getPwd()));
        loginResponse.setInviteCode(user.getInviteCode());
        return loginResponse;
    }

    /**
     * 手机号验证码登录
     *
     * @param loginRequest 登录请求信息
     * @return LoginResponse
     */
    @Override
    public LoginResponse phoneLogin(LoginMobileRequest loginRequest) {
        //检测验证码
//        checkValidateCode(loginRequest.getPhone(), loginRequest.getCaptcha());
        Integer spreadPid = Optional.ofNullable(loginRequest.getSpreadPid()).orElse(0);
        //查询手机号信息
        User user = userService.getByPhone(loginRequest.getPhone());
        if (ObjectUtil.isNull(user)) {// 此用户不存在，走新用户注册流程
            user = userService.registerPhone(loginRequest.getPhone(), spreadPid);
        } else {
            if (!user.getStatus()) {
                throw new CrmebException("当前账户已禁用，请联系管理员！");
            }
            if (user.getSpreadUid().equals(0) && spreadPid > 0) {
                // 绑定推广关系
                bindSpread(user, spreadPid);
            }
            // 记录最后一次登录时间
            user.setLastLoginTime(DateUtil.nowDateTime());
            boolean b = userService.updateById(user);
            if (!b) {
                logger.error("用户登录时，记录最后一次登录时间出错,uid = " + user.getUid());
            }
        }
        //生成token
        LoginResponse loginResponse = new LoginResponse();
        String token = tokenComponent.createToken(user);
        loginResponse.setToken(token);
        loginResponse.setUid(user.getUid());
        loginResponse.setNikeName(user.getNickname());
        loginResponse.setPhone(user.getPhone());
        loginResponse.setInviteCode(user.getInviteCode());
        loginResponse.setIsNewUser(StringUtil.isEmpty(user.getPwd()));
        return loginResponse;
    }

    /**
     * 检测手机验证码
     *
     * @param phone 手机号
     * @param code  验证码
     */
    private void checkValidateCode(String phone, String code) {
        Object validateCode = redisUtil.get(SmsConstants.SMS_VALIDATE_PHONE + phone);
        if (ObjectUtil.isNull(validateCode)) {
            throw new CrmebException("验证码已过期");
        }
        if (!validateCode.toString().equals(code)) {
            throw new CrmebException("验证码错误");
        }
        //删除验证码
        redisUtil.delete(SmsConstants.SMS_VALIDATE_PHONE + phone);
    }

    /**
     * 绑定分销关系
     *
     * @param user      User 用户user类
     * @param spreadUid Integer 推广人id
     * @return Boolean
     * 1.判断分销功能是否启用
     * 2.判断分销模式
     * 3.根据不同的分销模式校验
     * 4.指定分销，只有分销员才可以分销，需要spreadUid是推广员才可以绑定
     * 5.满额分销，同上
     * 6.人人分销，可以直接绑定
     */
    @Override
    public Boolean bindSpread(User user, Integer spreadUid) {
        Boolean checkBingSpread = userService.checkBingSpread(user, spreadUid, "old");
        if (!checkBingSpread) return false;

        user.setSpreadUid(spreadUid);
        user.setSpreadTime(DateUtil.nowDateTime());

        Boolean execute = transactionTemplate.execute(e -> {
            userService.updateById(user);
            userService.updateSpreadCountByUid(spreadUid, "add");
            return Boolean.TRUE;
        });
        if (!execute) {
            logger.error(StrUtil.format("绑定推广人时出错，userUid = {}, spreadUid = {}", user.getUid(), spreadUid));
        }
        return execute;
    }

    /**
     * 推出登录
     *
     * @param request HttpServletRequest
     */
    @Override
    public void loginOut(HttpServletRequest request) {
        tokenComponent.logout(request);
    }


    /**
     * tiktok授权登录实现
     *
     * @param code      授权code
     * @param spreadUid 推荐人
     * @return
     */
    @Override
    public LoginResponse tiktokAuthorizeLogin(String code, String codeVerifier, Integer spreadUid, String platform) {
        // 通过code获取获取公众号授权信息
        TikTokOauthToken oauthToken = tikTokNewService.getOauth2AccessToken(code, codeVerifier, platform);
        //获取失败
        if (oauthToken == null) {
            throw new CrmebException("第三方TikTok登录请求异常");
        }
        if (oauthToken.getError() != null) {
            throw new CrmebException("第三方TikTok登录请求异常::" + oauthToken.getError().get("message"));
        }
        //检测是否存在
        UserToken userToken = userTokenService.getByOpenidAndType(oauthToken.getOpenId(),
                Constants.THIRD_LOGIN_TOKEN_TYPE_TIKTOK);
        LoginResponse loginResponse = new LoginResponse();
        if (ObjectUtil.isNotNull(userToken)) {// 已存在，正常登录
            User user = userService.getById(userToken.getUid());
            if (!user.getStatus()) {
                throw new CrmebException("当前账户已禁用，请联系管理员！");
            }
            // 记录最后一次登录时间
            user.setLastLoginTime(DateUtil.nowDateTime());
            userService.updateById(user);
            try {
                String token = tokenComponent.createToken(user);
                loginResponse.setToken(token);
            } catch (Exception e) {
                logger.error(StrUtil.format("登录生成token失败，uid={}", user.getUid()));
            }
            loginResponse.setType("login");
            loginResponse.setUid(user.getUid());
            loginResponse.setNikeName(user.getNickname());
            loginResponse.setPhone(user.getPhone());
            return loginResponse;
        }
        // 没有用户，走创建用户流程
        // 获取tiktok的用户信息之后创建用户
        String nickname = tikTokNewService.getTiktokAccount(oauthToken.getAccessToken(), oauthToken.getOpenId());
        RegisterThirdUserRequest registerThirdUserRequest = new RegisterThirdUserRequest();
        registerThirdUserRequest.setOpenId(oauthToken.getOpenId());
        registerThirdUserRequest.setSpreadPid(spreadUid);
        registerThirdUserRequest.setType(Constants.USER_LOGIN_TYPE_TIKTOK);
        if (nickname.isEmpty()) {
            registerThirdUserRequest.setNickName("user::" + oauthToken.getOpenId());
        }else {
            registerThirdUserRequest.setNickName(nickname);
        }
        String key = SecureUtil.md5(oauthToken.getOpenId());
        try {
            Boolean execute = transactionTemplate.execute(e -> {
                //注册用户
                User user = userService.registerByThird(registerThirdUserRequest);
                //保存用户信息
                userService.save(user);
                user = userService.getByAccount(user.getAccount());
                //绑定token
                userTokenService.bind(registerThirdUserRequest.getOpenId(), Constants.THIRD_LOGIN_TOKEN_TYPE_TIKTOK, user.getUid());
                String token = tokenComponent.createToken(user);
                loginResponse.setToken(token);
                loginResponse.setUid(user.getUid());
                loginResponse.setNikeName(user.getNickname());
                loginResponse.setType("login");
                loginResponse.setKey(key);
                return Boolean.TRUE;
            });
        } catch (Exception e) {
            logger.error(StrUtil.format("自动登录生成token失败，code={}", code));
        }
        return loginResponse;
    }
}
