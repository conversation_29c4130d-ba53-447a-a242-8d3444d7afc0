package com.genco.service.service;

import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.vo.PaymentRequestVo;

/**
 * 支付策略接口
 */
public interface PaymentStrategy {

    /**
     * 处理支付
     *
     * @param paymentRequest 支付请求
     * @return OrderPayResultResponse
     */
    OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest);

    /**
     * 查询支付结果
     *
     * @param orderNo 订单编号
     * @return PaymentQueryResultResponse
     */
    PaymentQueryResultResponse queryPaymentResult(String orderNo);

    /**
     * 获取支付渠道
     *
     * @return 支付类型
     */
    String getPayChannel();
} 