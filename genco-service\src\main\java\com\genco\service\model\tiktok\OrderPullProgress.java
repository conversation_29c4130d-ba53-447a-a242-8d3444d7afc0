package com.genco.service.model.tiktok;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 多平台订单分页任务生成进度表实体
 * 记录每一轮次订单拉取任务的生成进度和状态
 */
@Data
@TableName("es_order_pull_progress")
public class OrderPullProgress implements Serializable {
    
    // 进度状态常量
    public static final int STATUS_PENDING = 0;    // 待生成
    public static final int STATUS_PROCESSING = 1; // 生成中
    public static final int STATUS_SUCCESS = 2;    // 成功
    public static final int STATUS_FAILED = 3;     // 失败
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 平台标识（如 tiktok、shopee）
     */
    private String platform;
    /**
     * 拉取订单的起始时间
     */
    private Date startTime;
    /**
     * 拉取订单的结束时间
     */
    private Date endTime;
    /**
     * 拉取批次号/轮次号
     */
    private String batchNo;
    /**
     * 最后处理的页码
     */
    private Integer lastPageNo;
    /**
     * 最后的分页token
     */
    private String lastPageToken;
    /**
     * 进度状态：0-待生成 1-生成中 2-成功 3-失败
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
} 