<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.genco.service.dao.tiktok.OrderPullProgressDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.genco.service.model.tiktok.OrderPullProgress">
        <id column="id" property="id" />
        <result column="platform" property="platform" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="batch_no" property="batchNo" />
        <result column="last_page_no" property="lastPageNo" />
        <result column="last_page_token" property="lastPageToken" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, platform, start_time, end_time, batch_no, last_page_no, last_page_token, status, remark, update_time
    </sql>

</mapper> 