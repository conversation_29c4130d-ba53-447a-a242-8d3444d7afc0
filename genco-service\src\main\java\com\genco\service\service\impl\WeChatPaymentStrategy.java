package com.genco.service.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.genco.common.constants.Constants;
import com.genco.common.constants.PayConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.user.UserToken;
import com.genco.common.response.OrderPayResultResponse;
import com.genco.common.response.PaymentQueryResultResponse;
import com.genco.common.utils.CrmebUtil;
import com.genco.common.utils.WxPayUtil;
import com.genco.common.vo.*;
import com.genco.service.service.PaymentStrategy;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.UserTokenService;
import com.genco.service.service.WechatNewService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 * 微信支付策略实现
 */
@Slf4j
@Service
public class WeChatPaymentStrategy implements PaymentStrategy {

    @Autowired
    private UserTokenService userTokenService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private WechatNewService wechatNewService;

    @Override
    public OrderPayResultResponse processPayment(PaymentRequestVo paymentRequest) {
        // 预下单
        Map<String, String> unifiedorder = unifiedorder(paymentRequest);

        OrderPayResultResponse response = new OrderPayResultResponse();
        response.setOrderNo(paymentRequest.getOrderNo());
        response.setPayType(paymentRequest.getPayType());
        response.setPayChannel(PayConstants.PAY_TYPE_WE_CHAT);
        response.setStatus(true);
        response.setAmount(paymentRequest.getAmount());
        response.setOutTradeNo(unifiedorder.get("out_trade_no"));

        WxPayJsResultVo vo = new WxPayJsResultVo();
        vo.setAppId(unifiedorder.get("appId"));
        vo.setNonceStr(unifiedorder.get("nonceStr"));
        vo.setPackages(unifiedorder.get("package"));
        vo.setSignType(unifiedorder.get("signType"));
        vo.setTimeStamp(unifiedorder.get("timeStamp"));
        vo.setPaySign(unifiedorder.get("paySign"));

        // 根据支付来源设置不同的支付类型
        if ("h5".equals(paymentRequest.getFrom())) {
            vo.setMwebUrl(unifiedorder.get("mweb_url"));
            response.setPayType(PayConstants.PAY_CHANNEL_WE_CHAT_H5);
        } else if ("app".equals(paymentRequest.getFrom())) {
            vo.setPartnerid(unifiedorder.get("partnerid"));
        }

        response.setJsConfig(vo);

        return response;
    }

    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        log.info("微信支付查询支付结果开始，订单号：{}", orderNo);

        try {
            // 根据订单号前缀判断业务类型
            String bizType = "unknown";
            if (orderNo.startsWith("wxNo") || orderNo.startsWith("order")) {
                bizType = PayConstants.BIZ_TYPE_ORDER;
            } else if (orderNo.startsWith("recharge")) {
                bizType = PayConstants.BIZ_TYPE_RECHARGE;
            }

            // 获取appid、mch_id、signKey
            String appId = "";
            String mchId = "";
            String signKey = "";

            // 这里需要根据具体的业务场景获取对应的配置
            // 暂时使用公众号配置作为默认配置
            appId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_ID);
            mchId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_MCH_ID);
            signKey = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_KEY);

            // 生成查询订单对象
            Map<String, String> payVo = getWxChantQueryPayVo(orderNo, appId, mchId, signKey);

            // 查询订单信息
            MyRecord record = wechatNewService.payOrderQuery(payVo);

            // 构建返回结果
            PaymentQueryResultResponse result = new PaymentQueryResultResponse();
            result.setOrderNo(orderNo);
            result.setOutTradeNo(orderNo);
            result.setPayChannel(PayConstants.PAY_TYPE_WE_CHAT);
            result.setBizType(bizType);
            result.setTransactionId(record.getStr("transaction_id"));
            result.setPayTime(record.getStr("time_end"));

            // 设置支付状态
            if ("SUCCESS".equals(record.getStr("trade_state"))) {
                result.setPaid(true);
                result.setAmount(new BigDecimal(record.getInt("total_fee")).divide(new BigDecimal("100")));
            } else {
                result.setPaid(false);
                result.setAmount(BigDecimal.ZERO);
            }

            log.info("微信支付查询支付结果成功，订单号：{}，支付状态：{}", orderNo, result.getPaid());
            return result;

        } catch (Exception e) {
            log.error("微信支付查询支付结果异常，订单号：{}", orderNo, e);
            throw new CrmebException("查询失败：" + e.getMessage());
        }
    }

    @Override
    public String getPayChannel() {
        return PayConstants.PAY_TYPE_WE_CHAT;
    }

    /**
     * 预下单
     *
     * @param paymentRequest 支付请求
     * @return 预下单返回对象
     */
    private Map<String, String> unifiedorder(PaymentRequestVo paymentRequest) {
        // 获取用户openId
        // 根据支付来源来判断获取公众号openId还是小程序openId
        UserToken userToken = new UserToken();
        if ("public".equals(paymentRequest.getFrom())) {// 公众号
            userToken = userTokenService.getTokenByUserId(paymentRequest.getUid(), 1);
        } else if ("routine".equals(paymentRequest.getFrom())) {// 小程序
            userToken = userTokenService.getTokenByUserId(paymentRequest.getUid(), 2);
        } else if ("h5".equals(paymentRequest.getFrom())) {// H5
            userToken.setToken("");
        }
        if (ObjectUtil.isNull(userToken)) {
            throw new CrmebException("该用户没有openId");
        }

        // 获取appid、mch_id
        // 微信签名key
        String appId = "";
        String mchId = "";
        String signKey = "";
        if ("public".equals(paymentRequest.getFrom())) {// 公众号
            appId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_ID);
            mchId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_MCH_ID);
            signKey = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_KEY);
        } else if ("routine".equals(paymentRequest.getFrom())) {// 小程序
            appId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_ROUTINE_APP_ID);
            mchId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_ROUTINE_MCH_ID);
            signKey = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_ROUTINE_APP_KEY);
        } else if ("h5".equals(paymentRequest.getFrom())) {// H5,使用公众号的
            appId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_ID);
            mchId = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_MCH_ID);
            signKey = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_PAY_WE_CHAT_APP_KEY);
        }

        // 获取微信预下单对象
        CreateOrderRequestVo unifiedorderVo = getUnifiedorderVo(paymentRequest, userToken.getToken(), appId, mchId, signKey);
        // 预下单（统一下单）
        CreateOrderResponseVo responseVo = wechatNewService.payUnifiedorder(unifiedorderVo);
        // 组装前端预下单参数
        Map<String, String> map = new HashMap<>();
        map.put("appId", unifiedorderVo.getAppid());
        map.put("nonceStr", unifiedorderVo.getAppid());
        map.put("package", "prepay_id=".concat(responseVo.getPrepayId()));
        map.put("signType", PayConstants.WX_PAY_SIGN_TYPE_MD5);
        map.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
        map.put("paySign", WxPayUtil.getSign(map, signKey));
        map.put("out_trade_no", unifiedorderVo.getOut_trade_no());

        return map;
    }

    /**
     * 获取微信预下单对象
     *
     * @param paymentRequest 支付请求
     * @param openid         openid
     * @param appId          appId
     * @param mchId          mchId
     * @param signKey        signKey
     * @return CreateOrderRequestVo
     */
    private CreateOrderRequestVo getUnifiedorderVo(PaymentRequestVo paymentRequest, String openid, String appId, String mchId, String signKey) {

        // 获取域名
        String domain = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_SITE_URL);
        String apiDomain = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_API_URL);

        // 根据业务类型设置不同的attach
        String attachType = PayConstants.BIZ_TYPE_ORDER.equals(paymentRequest.getBizType()) ?
                Constants.SERVICE_PAY_TYPE_ORDER :
                Constants.SERVICE_PAY_TYPE_RECHARGE;
        AttachVo attachVo = new AttachVo(attachType, paymentRequest.getUid());
        CreateOrderRequestVo vo = new CreateOrderRequestVo();

        vo.setAppid(appId);
        vo.setMch_id(mchId);
        vo.setNonce_str(WxPayUtil.getNonceStr());
        vo.setSign_type(PayConstants.WX_PAY_SIGN_TYPE_MD5);
        String siteName = systemConfigService.getValueByKeyException(Constants.CONFIG_KEY_SITE_NAME);
        // 因商品名称在微信侧超长更换为网站名称
        vo.setBody(siteName);
        vo.setAttach(JSONObject.toJSONString(attachVo));
        vo.setOut_trade_no(CrmebUtil.getOrderNo("wxNo"));
        // 订单中使用的是BigDecimal,这里要转为Integer类型
        vo.setTotal_fee(paymentRequest.getAmount().multiply(BigDecimal.TEN).multiply(BigDecimal.TEN).intValue());
        vo.setSpbill_create_ip(paymentRequest.getIp());
        vo.setNotify_url(apiDomain + PayConstants.WX_PAY_NOTIFY_API_URI);
        vo.setTrade_type(PayConstants.WX_PAY_TRADE_TYPE_JS);
        vo.setOpenid(openid);
        if ("h5".equals(paymentRequest.getFrom())) {// H5
            vo.setTrade_type(PayConstants.WX_PAY_TRADE_TYPE_H5);
            vo.setOpenid(null);
        }

        return vo;
    }

    /**
     * 获取微信查询支付对象
     *
     * @param orderNo 订单号
     * @param appId   appId
     * @param mchId   mchId
     * @param signKey signKey
     * @return Map<String, String>
     */
    private Map<String, String> getWxChantQueryPayVo(String orderNo, String appId, String mchId, String signKey) {
        Map<String, String> map = new HashMap<>();
        map.put("appid", appId);
        map.put("mch_id", mchId);
        map.put("out_trade_no", orderNo);
        map.put("nonce_str", WxPayUtil.getNonceStr());
        map.put("sign", WxPayUtil.getSign(map, signKey));
        return map;
    }
} 