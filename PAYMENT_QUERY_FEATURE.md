# 支付查询功能实现

## 概述

为了支持多种支付渠道的支付结果查询，我们在策略模式的基础上新增了支付查询功能。该功能支持HaiPay、微信支付、余额支付等多种支付渠道的查询，并且兼容订单支付和充值支付两种业务场景。

## 功能特性

1. **多支付渠道支持**：支持HaiPay、微信支付、余额支付、支付宝、线下支付、Xendit等多种支付渠道
2. **业务类型兼容**：支持订单支付和充值支付两种业务场景
3. **智能渠道识别**：自动根据订单号前缀或数据库记录识别支付渠道
4. **统一查询接口**：提供统一的查询接口，简化前端调用
5. **详细查询结果**：返回支付状态、金额、时间、交易流水号等详细信息

## 架构设计

### 核心组件

1. **PaymentStrategy** - 支付策略接口（新增查询方法）
2. **PaymentQueryService** - 支付查询服务接口
3. **PaymentQueryServiceImpl** - 支付查询服务实现
4. **PaymentQueryResultResponse** - 支付查询结果响应对象
5. **HaiPayQueryRequestVo** - HaiPay查询请求VO
6. **HaiPayQueryResponseVo** - HaiPay查询响应VO

### 策略实现类

- `HaiPayPaymentStrategy` - HaiPay支付策略（已实现查询）
- `WeChatPaymentStrategy` - 微信支付策略（已实现查询）
- `BalancePaymentStrategy` - 余额支付策略（已实现查询）
- `AlipayPaymentStrategy` - 支付宝支付策略（查询待实现）
- `OfflinePaymentStrategy` - 线下支付策略（查询待实现）
- `XenditPaymentStrategy` - Xendit支付策略（查询待实现）

## API接口

### 1. 查询支付结果（新版本）

```
GET /api/front/pay/query?orderNo={orderNo}
```

**参数说明：**
- `orderNo` - 订单编号（必填）

**返回示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "orderNo": "recharge123456",
    "outTradeNo": "haipay_order_789",
    "paid": true,
    "amount": 100.00,
    "payChannel": "haipay",
    "payTime": "2024-01-01 12:00:00",
    "transactionId": "HAI123456789",
    "bizType": "recharge"
  }
}
```

### 2. 根据支付渠道查询支付结果

```
GET /api/front/pay/queryByChannel?orderNo={orderNo}&payChannel={payChannel}
```

**参数说明：**
- `orderNo` - 订单编号（必填）
- `payChannel` - 支付渠道（必填）

**支持的支付渠道：**
- `haipay` - HaiPay支付
- `weixin` - 微信支付
- `yue` - 余额支付
- `alipay` - 支付宝
- `offline` - 线下支付
- `xendit` - Xendit支付

### 3. 查询支付结果（兼容旧版本）

```
GET /api/front/pay/queryPayResult?orderNo={orderNo}
```

**说明：** 此接口仅支持微信支付查询，返回布尔值表示是否支付成功。

## HaiPay查询实现

### 查询接口

根据HaiPay文档，我们实现了代收查询接口：

- **接口地址**：`https://uat-interface.haipay.asia/idr/collect/query`
- **请求方式**：POST
- **Content-Type**：application/json

### 请求参数

```json
{
  "appId": 123456,
  "orderId": "recharge123456",
  "sign": "签名"
}
```

### 响应参数

```json
{
  "status": "1",
  "error": "0",
  "msg": "success",
  "data": {
    "orderId": "recharge123456",
    "orderNo": "haipay_order_789",
    "status": "1",
    "amount": "100.00",
    "payTime": "2024-01-01 12:00:00",
    "bankCode": "BCA",
    "bankNo": "014",
    "transactionId": "HAI123456789"
  },
  "sign": "响应签名"
}
```

### 支付状态说明

- `1` - 支付成功
- `0` - 未支付
- `2` - 支付失败

## 使用方式

### 1. 自动识别支付渠道查询

```javascript
// 前端调用示例
fetch('/api/front/pay/query?orderNo=recharge123456')
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      const result = data.data;
      console.log('支付状态:', result.paid);
      console.log('支付金额:', result.amount);
      console.log('支付渠道:', result.payChannel);
      console.log('支付时间:', result.payTime);
    }
  });
```

### 2. 指定支付渠道查询

```javascript
// 前端调用示例
fetch('/api/front/pay/queryByChannel?orderNo=recharge123456&payChannel=haipay')
  .then(response => response.json())
  .then(data => {
    if (data.code === 200) {
      const result = data.data;
      console.log('支付状态:', result.paid);
    }
  });
```

### 3. Java服务端调用

```java
@Autowired
private PaymentQueryService paymentQueryService;

// 自动识别支付渠道查询
PaymentQueryResultResponse result = paymentQueryService.queryPaymentResult("recharge123456");

// 指定支付渠道查询
PaymentQueryResultResponse result = paymentQueryService.queryPaymentResult("recharge123456", "haipay");
```

## 配置说明

### HaiPay配置

```properties
# HaiPay商户配置
haipay_app_id=your_app_id
haipay_private_key=your_private_key
haipay_secret_key=your_secret_key
haipay_public_key=your_public_key
haipay_api_url=https://uat-interface.haipay.asia/idr/collect/apply
haipay_query_api_url=https://uat-interface.haipay.asia/idr/collect/query
```

### 微信支付配置

```properties
# 公众号支付配置
pay_wechat_app_id=your_app_id
pay_wechat_mch_id=your_mch_id
pay_wechat_app_key=your_app_key

# 小程序支付配置
pay_routine_app_id=your_mini_app_id
pay_routine_mch_id=your_mch_id
pay_routine_app_key=your_app_key
```

## 扩展新的支付渠道查询

1. 实现 `PaymentStrategy` 接口的 `queryPaymentResult` 方法
2. 在对应的支付策略类中添加查询逻辑
3. 添加相应的配置参数（如需要）

### 示例：添加新的支付渠道查询

```java
@Service
public class NewPaymentStrategy implements PaymentStrategy {
    
    @Override
    public PaymentQueryResultResponse queryPaymentResult(String orderNo) {
        // 实现查询逻辑
        PaymentQueryResultResponse result = new PaymentQueryResultResponse();
        result.setOrderNo(orderNo);
        result.setPayChannel("new_payment");
        result.setPaid(true);
        result.setAmount(new BigDecimal("100.00"));
        return result;
    }
    
    // ... 其他方法
}
```

## 注意事项

1. 所有支付策略都支持订单支付和充值支付两种业务类型
2. 查询接口会自动根据订单号前缀或数据库记录识别支付渠道
3. 如果无法识别支付渠道，会抛出相应的异常信息
4. 余额支付是即时支付，查询结果基于数据库中的支付状态
5. 其他支付方式需要调用第三方API进行查询

## 版本历史

- v1.0.0 - 初始版本，支持微信支付查询
- v2.0.0 - 重构为策略模式，支持多种支付渠道查询
- v2.1.0 - 新增HaiPay支付查询支持
- v2.2.0 - 新增统一查询接口，支持自动渠道识别 