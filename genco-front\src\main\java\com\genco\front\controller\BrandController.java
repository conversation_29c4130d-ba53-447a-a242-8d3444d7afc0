package com.genco.front.controller;


import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.response.CommonResult;
import com.genco.common.response.StoreBrandResponse;
import com.genco.service.service.BrandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 品牌控制器
 */
@Slf4j
@RestController("BrandController")
@RequestMapping("api/front")
@Api(tags = "品牌")
public class BrandController {

    @Autowired
    private BrandService brandService;

    /**
     * 品牌列表
     */
    @ApiOperation(value = "品牌列表")
    @RequestMapping(value = "/brand/list", method = RequestMethod.GET)
    public CommonResult<CommonPage<StoreBrandResponse>> getBrandList(@RequestParam(value = "type") Integer type,
                                                                     @Validated PageParamRequest pageParamRequest) {
        return CommonResult.success(brandService.getList(type, null, pageParamRequest));
    }
}



