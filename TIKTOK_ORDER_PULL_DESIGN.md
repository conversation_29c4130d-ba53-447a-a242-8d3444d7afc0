# TikTok/Shopee 多平台订单全量拉取与同步系统设计文档

## 1. 需求与目标
- 实现高效、可扩展、支持分布式的多平台（TikTok、Shopee等）订单全量拉取与同步系统。
- 支持分页拉取、批次号、任务状态监控、失败重试、断点续拉、任务表/进度表监控、人工干预。
- 代码结构清晰，便于维护和扩展，支持未来更多平台。

## 2. 架构与核心表结构

### 2.1 任务表（es_order_pull_task）
- 记录每一页的拉取任务。
- 字段：平台、时间区间、批次号、页码、状态、重试次数、nextPageToken、唯一索引等。
- 支持多平台多批次高效查询。

### 2.2 进度表（es_order_pull_progress）
- 记录每个时间区间、批次、平台的分页任务生成进度。
- 字段：平台、时间区间、批次号、最后页码、最后token、状态等。
- 支持断点续生成。

### 2.3 Redis队列
- 任务分发队列：order_pull_task_queue:{platform}
- 失败队列：tiktok_order_pull_failed_queue

## 3. 核心流程

### 3.1 任务生成与分发
- 定时/手动生成指定时间区间、批次的分页拉取任务，写入任务表。
- 任务写入后推送到Redis分发队列，消费端异步处理。

### 3.2 任务消费与订单同步
- 消费端定时扫描任务表，抢占待拉取/可重试任务（乐观锁+CAS）。
- 拉取订单数据，处理分页（如有下一页生成新任务）。
- 成功则更新任务状态，失败则重试，重试超限后推送到失败队列。

### 3.3 失败队列与补偿
- 达到最大重试次数的任务推送到Redis失败队列（tiktok_order_pull_failed_queue）。
- 定时任务自动消费失败队列，尝试补偿拉取，成功则更新任务状态，失败则保留在队列。
- 支持人工通过接口/后台管理触发指定任务的补偿拉取。

## 4. 关键实现要点

- 任务表、进度表唯一索引、复合索引均包含platform和batch_no，支持多平台多批次。
- 任务生成与分发异步解耦，提升系统弹性和吞吐量。
- 消费端加分布式锁，防止并发重复处理，保证幂等和一致性。
- 失败重试机制健全，任务表重试次数自增，便于监控和补偿。
- 失败队列支持自动补偿和人工干预，极大提升系统可维护性。
- 代码结构清晰，接口抽象，便于扩展更多平台。

## 5. 扩展性设计

- 平台相关逻辑通过PlatformOrderSyncService接口抽象，TikTok、Shopee等平台分别实现。
- 任务表/进度表/调度/监控等主流程可复用，平台相关逻辑解耦。
- 新增平台仅需实现对应的同步Service和适配任务分发队列。

## 6. 典型时序图

```mermaid
graph TD
    A[任务生成] --> B[写入任务表]
    B --> C[推送到Redis分发队列]
    C --> D[消费端抢占任务]
    D --> E[订单同步]
    E -->|成功| F[任务状态=成功]
    E -->|失败| G[重试/失败队列]
    G -->|重试超限| H[推送失败队列]
    H --> I[定时/人工补偿]
    I -->|成功| F
    I -->|失败| H
```

## 7. 主要表结构示例

### es_order_pull_task
```sql
CREATE TABLE `es_order_pull_task` (
  `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `platform` VARCHAR(32) NOT NULL DEFAULT 'tiktok' COMMENT '平台标识',
  `start_time` DATETIME NOT NULL COMMENT '拉取订单的起始时间',
  `end_time` DATETIME NOT NULL COMMENT '拉取订单的结束时间',
  `batch_no` VARCHAR(64) NOT NULL COMMENT '拉取批次号/轮次号',
  `page_no` INT NOT NULL DEFAULT 1 COMMENT '当前页码',
  `next_page_token` VARCHAR(255) DEFAULT NULL COMMENT '分页token',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0-待拉取 1-拉取中 2-成功 3-失败',
  `retry_count` INT NOT NULL DEFAULT 0 COMMENT '重试次数',
  `last_pull_time` DATETIME DEFAULT NULL COMMENT '最后一次拉取时间',
  `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_platform_time_page_batch` (`platform`, `start_time`, `end_time`, `batch_no`, `page_no`),
  KEY `idx_platform_status_batch` (`platform`, `status`, `batch_no`),
  KEY `idx_platform_time_batch` (`platform`, `start_time`, `end_time`, `batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多平台订单分页拉取任务表';
```

### es_order_pull_progress
```sql
CREATE TABLE `es_order_pull_progress` (
  `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `platform` VARCHAR(32) NOT NULL DEFAULT 'tiktok' COMMENT '平台标识',
  `start_time` DATETIME NOT NULL,
  `end_time` DATETIME NOT NULL,
  `batch_no` VARCHAR(64) NOT NULL,
  `last_page_no` INT NOT NULL DEFAULT 0,
  `last_page_token` VARCHAR(255) DEFAULT NULL,
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0-进行中 1-完成 2-失败',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `remark` VARCHAR(255) DEFAULT NULL,
  UNIQUE KEY `uniq_platform_time_range_batch` (`platform`, `start_time`, `end_time`, `batch_no`),
  KEY `idx_platform_status_batch` (`platform`, `status`, `batch_no`),
  KEY `idx_platform_time_batch` (`platform`, `start_time`, `end_time`, `batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多平台订单分页任务生成进度表';
```

## 8. 典型代码要点
- 任务表/进度表MyBatis实体与DAO
- 任务生成、分发、消费、失败重试、补偿、人工接口等核心Service
- RedisUtil工具类用于队列操作
- 日志与监控完善，便于排查和人工干预

## 9. 结论
- 该方案实现了高并发、分布式、批次化、多平台的订单全量拉取与同步，兼顾高性能和可维护性。
- 任务表为主，Redis队列为辅，监控、重试、补拉、人工干预能力强。
- 代码和表结构高度可扩展，适合大中型电商/内容平台多平台订单同步场景。 