package com.genco.common.utils;

import org.slf4j.Logger;

/**
 * LoggerUtil 测试类
 * 用于验证LoggerUtil的功能
 * 
 * <AUTHOR>
 */
public class LoggerUtilTest {
    
    private static final Logger logger = LoggerUtil.getLogger(LoggerUtilTest.class);
    
    public static void main(String[] args) {
        LoggerUtilTest test = new LoggerUtilTest();
        test.testBasicLogging();
        test.testExceptionLogging();
        test.testFormatExceptionLogging();
        test.testComplexExceptionStack();
        test.testFormatLogging();
    }
    
    /**
     * 测试基本日志功能
     */
    public void testBasicLogging() {
        System.out.println("=== 测试基本日志功能 ===");
        
        // 摘要日志测试
        LoggerUtil.digestInfo(this.getClass(), "开始测试基本日志功能");
        LoggerUtil.digestDebug(logger, "调试信息");
        LoggerUtil.digestWarn(logger, "警告信息");
        
        // 详细日志测试
        LoggerUtil.detailInfo(logger, "详细日志信息 - 测试时间: {}", DateUtil.getNowTime());
        LoggerUtil.detailDebug(logger, "详细调试信息");
        LoggerUtil.detailWarn(logger, "详细警告信息");
        
        LoggerUtil.digestInfo(this.getClass(), "基本日志功能测试完成");
    }
    
    /**
     * 测试异常日志功能
     */
    public void testExceptionLogging() {
        System.out.println("=== 测试异常日志功能 ===");
        
        try {
            // 模拟异常
            throw new RuntimeException("测试异常");
        } catch (Exception e) {
            LoggerUtil.digestError(this.getClass(), "摘要错误日志", e);
            LoggerUtil.detailError(logger, "详细错误日志", e);
        }
    }
    
    /**
     * 测试格式化异常日志功能
     */
    public void testFormatExceptionLogging() {
        System.out.println("=== 测试格式化异常日志功能 ===");
        
        String userId = "12345";
        String orderId = "ORD20231201001";
        
        try {
            // 模拟异常
            throw new RuntimeException("订单处理异常");
        } catch (Exception e) {
            // 使用格式化字符串记录异常
            LoggerUtil.digestError(this.getClass(), "用户 {} 的订单 {} 处理失败", e, userId, orderId);
            LoggerUtil.detailError(logger, "用户 {} 的订单 {} 处理详细错误信息", e, userId, orderId);
            
            // 使用字符串名称
            LoggerUtil.digestError("OrderService", "订单 {} 处理异常", e, orderId);
            LoggerUtil.detailError("UserService", "用户 {} 操作异常", e, userId);
        }
    }
    
    /**
     * 测试复杂异常堆栈信息
     */
    public void testComplexExceptionStack() {
        System.out.println("=== 测试复杂异常堆栈信息 ===");
        
        try {
            // 创建一个复杂的异常链
            throw createComplexException();
        } catch (Exception e) {
            LoggerUtil.digestError(this.getClass(), "复杂异常测试", e);
            LoggerUtil.detailError(logger, "复杂异常详细测试", e);
            
            // 测试堆栈信息获取方法
            String stackTrace = LoggerUtil.getStackTrace(e);
            System.out.println("手动获取的堆栈信息:");
            System.out.println(stackTrace);
        }
    }
    
    /**
     * 创建一个复杂的异常链用于测试
     */
    private Exception createComplexException() {
        try {
            // 第一层异常
            throw new IllegalArgumentException("参数错误");
        } catch (Exception e1) {
            try {
                // 第二层异常
                throw new RuntimeException("运行时异常", e1);
            } catch (Exception e2) {
                // 第三层异常
                return new Exception("最终异常", e2);
            }
        }
    }
    
    /**
     * 测试格式化日志功能
     */
    public void testFormatLogging() {
        System.out.println("=== 测试格式化日志功能 ===");
        
        String userId = "12345";
        String action = "登录";
        String ip = "***********";
        
        // 使用格式化字符串
        LoggerUtil.digestInfo(this.getClass(), "用户 {} 执行 {} 操作", userId, action);
        LoggerUtil.detailInfo(logger, "用户 {} 执行 {} 操作，IP地址: {}", userId, action, ip);
        
        // 使用字符串名称获取Logger
        LoggerUtil.digestInfo("UserService", "用户服务调用完成");
        LoggerUtil.detailInfo("OrderService", "订单服务处理中");
    }
} 