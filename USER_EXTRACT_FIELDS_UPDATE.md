# UserExtract 模型字段更新总结

## 概述

为 UserExtract（用户提现）模型新增了5个字段，以支持更详细的提现处理流程。

## 新增字段

### 1. 实际到账金额 (actualAmount)

- **类型**: BigDecimal
- **描述**: 用户实际收到的金额（扣除手续费后）
- **数据库字段**: `actual_amount` decimal(8,2)

### 2. 手续费 (serviceFee)

- **类型**: BigDecimal
- **描述**: 提现过程中产生的手续费
- **数据库字段**: `service_fee` decimal(8,2)

### 3. 操作人 (operator)

- **类型**: String
- **描述**: 处理提现申请的操作员
- **数据库字段**: `operator` varchar(100)

### 4. 转账时间 (transferTime)

- **类型**: Date
- **描述**: 实际转账的时间
- **数据库字段**: `transfer_time` datetime

### 5. 转账结果 (transferResult)

- **类型**: String
- **描述**: 转账的结果状态
- **数据库字段**: `transfer_result` varchar(255)

## 文件修改清单

### 1. 模型类更新

- **文件**: `crmeb-common/src/main/java/com/genco/common/model/finance/UserExtract.java`
- **修改**: 添加了5个新字段的属性和注解

### 2. 数据库DDL

- **文件**: `sql/add_fields_to_user_extract.sql`
- **内容**:
    - ALTER TABLE 语句添加新字段
    - 创建相关索引
    - 可选的现有数据更新脚本

### 3. 新增请求类

- **文件**: `crmeb-common/src/main/java/com/genco/common/request/UserExtractDealRequest.java`
- **功能**: 用于后台处理提现申请时传递详细信息的请求对象

### 4. 服务接口更新

- **文件**: `genco-service/src/main/java/com/genco/service/service/UserExtractService.java`
- **修改**: 添加了 `dealExtractWithDetails` 方法

### 5. 服务实现更新

- **文件**: `genco-service/src/main/java/com/genco/service/service/impl/UserExtractServiceImpl.java`
- **修改**:
    - 添加了 `dealExtractWithDetails` 方法实现
    - 导入了新的请求类

### 6. 控制器更新

- **文件**: `genco-admin/src/main/java/com/genco/admin/controller/UserExtractController.java`
- **修改**:
    - 添加了新的API端点 `/deal/detailed`
    - 导入了新的请求类

## 数据库字段说明

| 字段名             | 类型       | 长度  | 允许空 | 默认值  | 说明     |
|-----------------|----------|-----|-----|------|--------|
| actual_amount   | decimal  | 8,2 | YES | NULL | 实际到账金额 |
| service_fee     | decimal  | 8,2 | YES | NULL | 手续费    |
| operator        | varchar  | 100 | YES | NULL | 操作人    |
| transfer_time   | datetime | -   | YES | NULL | 转账时间   |
| transfer_result | varchar  | 255 | YES | NULL | 转账结果   |

## 索引优化

为以下字段添加了索引以提高查询性能：

- `transfer_time` - 转账时间索引
- `operator` - 操作人索引
- `transfer_result` - 转账结果索引

## API 端点

### 新增API

- **路径**: `/api/admin/finance/apply/deal/detailed`
- **方法**: POST
- **权限**: `admin:finance:apply:update`
- **功能**: 提现处理（包含详细字段）

### 请求参数

```json
{
  "id": 1,
  "actualAmount": 95.00,
  "serviceFee": 5.00,
  "operator": "admin",
  "transferResult": "成功",
  "voucherImage": "http://example.com/voucher.jpg",
  "remark": "转账成功"
}
```

## 注意事项

1. **保留原有字段**: `extractPrice` 字段保持不变，继续作为提现申请金额
2. **向后兼容**: 原有的 `dealExtract` 方法仍然可用
3. **数据迁移**: 新字段允许为空，不会影响现有数据
4. **权限控制**: 新API端点使用相同的权限控制

## 执行步骤

1. 执行 `sql/add_fields_to_user_extract.sql` 中的DDL语句
2. 重新编译项目
3. 重启服务
4. 测试新的API端点

## 状态说明

- `status = 0`: 审核中
- `status = 1`: 已审核
- `status = 2`: 已打款
- `status = -1`: 未通过

新的 `transferResult` 字段可以存储更详细的转账结果信息，如"成功"、"失败"、"处理中"等。 