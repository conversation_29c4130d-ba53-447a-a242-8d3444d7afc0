# LoggerUtil 使用说明

## 概述

`LoggerUtil` 是一个基于 SLF4J + Logback 的日志工具类，支持摘要日志（digest）和详细日志（detail）两种模式。

## 特性

- 支持摘要日志和详细日志分类
- 支持多种日志级别（DEBUG、INFO、WARN、ERROR）
- 支持格式化日志消息
- 支持异常日志记录
- 提供多种获取Logger的方式
- 线程安全的Logger缓存

## 日志分类

### 摘要日志（Digest）

- 用于记录关键业务信息
- 日志前缀：`[DIGEST]`
- 适合记录重要的业务节点信息

### 详细日志（Detail）

- 用于记录详细的调试信息
- 日志前缀：`[DETAIL]`
- 适合记录完整的业务流程信息

## 使用方法

### 1. 基本使用

```java
import utils.com.genco.common.LoggerUtil;
import org.slf4j.Logger;

public class MyService {
    private static final Logger logger = LoggerUtil.getLogger(MyService.class);

    public void processData(String data) {
        // 摘要日志
        LoggerUtil.digestInfo(this.getClass(), "开始处理数据: {}", data);

        // 详细日志
        LoggerUtil.detailInfo(logger, "数据处理详细信息 - 数据长度: {}, 处理时间: {}",
                data.length(), System.currentTimeMillis());

        // 业务逻辑...

        // 完成日志
        LoggerUtil.digestInfo(this.getClass(), "数据处理完成");
    }
}
```

### 2. 异常处理

```java
public void processWithException() {
    try {
        // 业务逻辑
        throw new RuntimeException("业务异常");
    } catch (Exception e) {
        // 摘要错误日志（自动包含完整堆栈信息）
        LoggerUtil.digestError(this.getClass(), "业务处理失败", e);

        // 详细错误日志（自动包含完整堆栈信息）
        LoggerUtil.detailError(logger, "详细错误信息", e);

        // 格式化异常日志（自动包含完整堆栈信息）
        LoggerUtil.digestError(this.getClass(), "用户 {} 的订单 {} 处理失败", e, userId, orderId);
        LoggerUtil.detailError(logger, "用户 {} 的订单 {} 处理详细错误信息", e, userId, orderId);

        // 手动获取堆栈信息
        String stackTrace = LoggerUtil.getStackTrace(e);
        System.out.println("完整堆栈信息: " + stackTrace);
    }
}
```

### 3. 不同级别的日志

```java
// DEBUG级别
LoggerUtil.digestDebug(logger, "调试信息");

// INFO级别
LoggerUtil.

digestInfo(logger, "信息日志");

// WARN级别
LoggerUtil.

digestWarn(logger, "警告信息");

// ERROR级别
LoggerUtil.

digestError(logger, "错误信息");
```

### 4. 格式化日志

```java
// 使用格式化字符串
LoggerUtil.digestInfo(this.getClass(), "用户 {} 在 {} 登录成功",userId,loginTime);

// 使用Logger实例
        LoggerUtil.

detailInfo(logger, "订单 {} 金额 {} 处理完成",orderId, amount);
```

### 5. 获取Logger的方式

```java
// 方式1: 使用类名
Logger logger1 = LoggerUtil.getLogger(MyService.class);

// 方式2: 使用字符串名称
Logger logger2 = LoggerUtil.getLogger("MyService");

// 方式3: 直接使用类名打印日志
LoggerUtil.

digestInfo(MyService .class, "日志消息");
```

### 6. 完整方法列表

LoggerUtil提供了以下完整的方法集合：

#### 摘要日志方法

- `digestDebug(Logger, String)` - DEBUG级别摘要日志
- `digestInfo(Logger, String)` - INFO级别摘要日志
- `digestWarn(Logger, String)` - WARN级别摘要日志
- `digestError(Logger, String)` - ERROR级别摘要日志
- `digestError(Logger, String, Throwable)` - ERROR级别摘要日志（带异常）
- `digestError(Logger, String, Throwable, Object...)` - ERROR级别摘要日志（格式化，带异常）

#### 详细日志方法

- `detailDebug(Logger, String)` - DEBUG级别详细日志
- `detailInfo(Logger, String)` - INFO级别详细日志
- `detailWarn(Logger, String)` - WARN级别详细日志
- `detailError(Logger, String)` - ERROR级别详细日志
- `detailError(Logger, String, Throwable)` - ERROR级别详细日志（带异常）
- `detailError(Logger, String, Throwable, Object...)` - ERROR级别详细日志（格式化，带异常）

#### 使用类名的便捷方法

- `digestInfo(Class, String)` - INFO级别摘要日志
- `digestError(Class, String, Throwable)` - ERROR级别摘要日志（带异常）
- `digestError(Class, String, Throwable, Object...)` - ERROR级别摘要日志（格式化，带异常）
- `detailInfo(Class, String)` - INFO级别详细日志
- `detailError(Class, String, Throwable)` - ERROR级别详细日志（带异常）
- `detailError(Class, String, Throwable, Object...)` - ERROR级别详细日志（格式化，带异常）

#### 使用字符串名称的便捷方法

- `digestInfo(String, String)` - INFO级别摘要日志
- `digestError(String, String, Throwable)` - ERROR级别摘要日志（带异常）
- `digestError(String, String, Throwable, Object...)` - ERROR级别摘要日志（格式化，带异常）
- `detailInfo(String, String)` - INFO级别详细日志
- `detailError(String, String, Throwable)` - ERROR级别详细日志（带异常）
- `detailError(String, String, Throwable, Object...)` - ERROR级别详细日志（格式化，带异常）

#### 格式化方法

- `digestInfo(Logger, String, Object...)` - INFO级别摘要日志（格式化）
- `detailInfo(Logger, String, Object...)` - INFO级别详细日志（格式化）
- `digestInfo(Class, String, Object...)` - INFO级别摘要日志（格式化，使用类名）
- `detailInfo(Class, String, Object...)` - INFO级别详细日志（格式化，使用类名）

#### 堆栈信息工具方法

- `getStackTrace(Throwable)` - 获取异常的完整堆栈信息字符串

## 堆栈信息功能

LoggerUtil提供了增强的异常堆栈信息记录功能：

### 1. 自动堆栈信息记录

所有带异常参数的错误日志方法都会自动记录完整的堆栈信息，包括：

- 异常类型和消息
- 完整的调用堆栈
- 原因异常（如果有）

### 2. 手动获取堆栈信息

```java
try{
        // 业务逻辑
        }catch(Exception e){
// 手动获取格式化的堆栈信息
String stackTrace = LoggerUtil.getStackTrace(e);
    System.out.

println("完整堆栈信息:\n"+stackTrace);
}
```

### 3. 堆栈信息格式

```
异常类型: java.lang.RuntimeException
异常消息: 业务处理异常
完整堆栈信息:
	at com.example.MyService.processData(MyService.java:25)
	at com.example.MyController.handleRequest(MyController.java:15)
	...

原因异常: java.lang.IllegalArgumentException: 参数错误
	at com.example.Validator.validate(Validator.java:10)
	...
```

## 配置说明

### Logback配置文件

项目已经配置了完整的logback日志系统，LoggerUtil会自动使用现有的配置。如果需要专门的摘要日志和详细日志分离，可以参考以下配置：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false">
    <!-- 摘要日志输出器 -->
    <appender name="DIGEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/log_digest.log</file>
        <!-- 只记录包含[DIGEST]的日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <expression>return message.contains("[DIGEST]");</expression>
            </evaluator>
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>DENY</OnMismatch>
        </filter>
    </appender>

    <!-- 详细日志输出器 -->
    <appender name="DETAIL_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/log_detail.log</file>
        <!-- 只记录包含[DETAIL]的日志 -->
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator class="ch.qos.logback.classic.boolex.OnMarkerEvaluator">
                <expression>return message.contains("[DETAIL]");</expression>
            </evaluator>
            <OnMatch>ACCEPT</OnMatch>
            <OnMismatch>DENY</OnMismatch>
        </filter>
    </appender>
</configuration>
```

## 最佳实践

1. **摘要日志**：记录关键业务节点，如用户登录、订单创建、支付完成等
2. **详细日志**：记录完整的业务流程，便于问题排查
3. **异常日志**：同时记录摘要和详细错误信息
4. **性能考虑**：使用格式化字符串而不是字符串拼接
5. **日志级别**：根据环境调整日志级别，生产环境建议使用INFO级别

## 示例输出

```
{
"app": "Crmeb",
"timestamp":"2023-12-01 10:30:15.123",
"level": "INFO",
"thread": "main",
"class": "com.genco.common.utils.MyService",
"message": "[DIGEST] 用户登录成功，用户ID: 12345" }

{
"app": "Crmeb",
"timestamp":"2023-12-01 10:30:15.124",
"level": "INFO",
"thread": "main",
"class": "com.genco.common.utils.MyService",
"message": "[DETAIL] 用户登录详细信息 - IP: ***********, 时间: 2023-12-01 10:30:15, 设备: iPhone" }

{
"app": "Crmeb",
"timestamp":"2023-12-01 10:30:16.125",
"level": "ERROR",
"thread": "main",
"class": "com.genco.common.utils.MyService",
"message": "[DIGEST] 业务处理失败" }

{
"app": "Crmeb",
"timestamp":"2023-12-01 10:30:16.126",
"level": "ERROR",
"thread": "main",
"class": "com.genco.common.utils.MyService",
"message": "[DIGEST] 详细堆栈信息:\n异常类型: java.lang.RuntimeException\n异常消息: 业务处理异常\n完整堆栈信息:\n\tat com.genco.common.utils.MyService.processData(MyService.java:25)\n\tat com.genco.common.utils.MyController.handleRequest(MyController.java:15)" }
```

## 注意事项

1. 项目已配置SLF4J + Logback，无需额外依赖
2. LoggerUtil会自动使用项目现有的logback配置
3. 日志文件目录需要确保有写入权限
4. 建议在生产环境中配置日志轮转和清理策略
5. 摘要日志和详细日志会同时输出到控制台和文件 