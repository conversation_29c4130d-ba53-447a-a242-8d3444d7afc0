CREATE TABLE `es_order_pull_task` (
  `id` BIGINT NOT NULL AUTO_INCREMENT PRIMARY KEY,
  `platform` VARCHAR(32) NOT NULL DEFAULT 'tiktok' COMMENT '平台标识',
  `start_time` DATETIME NOT NULL COMMENT '拉取订单的起始时间',
  `end_time` DATETIME NOT NULL COMMENT '拉取订单的结束时间',
  `batch_no` VARCHAR(64) NOT NULL COMMENT '拉取批次号/轮次号',
  `page_no` INT NOT NULL DEFAULT 1 COMMENT '当前页码',
  `next_page_token` VARCHAR(255) DEFAULT NULL COMMENT '分页token',
  `status` TINYINT NOT NULL DEFAULT 0 COMMENT '0-待拉取 1-拉取中 2-成功 3-失败',
  `retry_count` INT NOT NULL DEFAULT 0 COMMENT '重试次数',
  `last_pull_time` DATETIME DEFAULT NULL COMMENT '最后一次拉取时间',
  `remark` VARCHAR(255) DEFAULT NULL COMMENT '备注',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY `uniq_platform_time_page_batch` (`platform`, `start_time`, `end_time`, `batch_no`, `page_no`),
  KEY `idx_platform_status_batch` (`platform`, `status`, `batch_no`),
  KEY `idx_platform_time_batch` (`platform`, `start_time`, `end_time`, `batch_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='多平台订单分页拉取任务表'; 