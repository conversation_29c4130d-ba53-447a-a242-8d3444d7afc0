package com.genco.common.vo;

import lombok.Data;

/**
 * HaiPay 支付回调参数对象
 * 仅用于接收和处理 HaiPay 支付平台回调参数（不含 sign 字段）。
 * 便于后续字段扩展和类型安全管理。
 */
@Data
public class HaiPayCallbackVo {
    /**
     * 支付金额（单位：分/厘，具体以平台为准）
     */
    private String amount;

    /**
     * HaiPay 平台生成的订单号（外部单号）
     */
    private String orderNo;

    /**
     * 商户系统订单号（如 recharge... 或 order...）
     */
    private String orderId;

    /**
     * 支付完成时间（格式如：2025-07-12 16:39:58）
     */
    private String payTime;

    /**
     * HaiPay 分配的商户 AppId
     */
    private Long appId;

    /**
     * 实际到账金额（单位：分/厘，具体以平台为准）
     */
    private String fee;

    /**
     * 币种（如：IDR）
     */
    private String currency;

    /**
     * 支付状态（如：2 表示支付成功）
     */
    private Integer status;

    private String sign;
} 