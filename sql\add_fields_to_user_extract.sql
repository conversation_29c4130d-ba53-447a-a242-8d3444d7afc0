-- 为 eb_user_extract 表添加新字段
-- 实际到账金额、手续费、操作人、转账时间、转账结果

ALTER TABLE `eb_user_extract` 
ADD COLUMN `actual_amount` decimal(8,2) DEFAULT NULL COMMENT '实际到账金额' AFTER `deal_remark`,
ADD COLUMN `service_fee` decimal(8,2) DEFAULT NULL COMMENT '手续费' AFTER `actual_amount`,
ADD COLUMN `operator` varchar(100) DEFAULT NULL COMMENT '操作人' AFTER `service_fee`,
ADD COLUMN `transfer_time` datetime DEFAULT NULL COMMENT '转账时间' AFTER `operator`,
ADD COLUMN `transfer_result` varchar(255) DEFAULT NULL COMMENT '转账结果' AFTER `transfer_time`;

-- 添加索引以提高查询性能
ALTER TABLE `eb_user_extract` 
ADD INDEX `idx_transfer_time` (`transfer_time`),
ADD INDEX `idx_operator` (`operator`),
ADD INDEX `idx_transfer_result` (`transfer_result`);

-- 更新现有记录的转账结果（可选）
-- UPDATE `eb_user_extract` SET `transfer_result` = '成功' WHERE `status` = 2;
-- UPDATE `eb_user_extract` SET `transfer_result` = '失败' WHERE `status` = -1;
-- UPDATE `eb_user_extract` SET `transfer_result` = '处理中' WHERE `status` = 0 OR `status` = 1; 