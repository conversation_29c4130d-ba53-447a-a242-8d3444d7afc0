package com.genco.service.model.tiktok;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 多平台订单分页拉取任务表实体
 * 记录每一页订单拉取任务的参数、状态和进度
 */
@Data
@TableName("es_order_pull_task")
public class OrderPullTask implements Serializable {
    
    // 任务状态常量
    public static final int STATUS_PENDING = 0;    // 待拉取
    public static final int STATUS_PROCESSING = 1; // 拉取中
    public static final int STATUS_SUCCESS = 2;    // 成功
    public static final int STATUS_FAILED = 3;     // 失败
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 平台标识（如 tiktok、shopee）
     */
    private String platform;
    /**
     * 拉取订单的起始时间
     */
    private Date startTime;
    /**
     * 拉取订单的结束时间
     */
    private Date endTime;
    /**
     * 拉取批次号/轮次号
     */
    private String batchNo;
    /**
     * 当前页码
     */
    private Integer pageNo;
    /**
     * 分页token（用于API请求下一页）
     */
    private String nextPageToken;
    /**
     * 任务状态：0-待拉取 1-拉取中 2-成功 3-失败
     */
    private Integer status;
    /**
     * 重试次数
     */
    private Integer retryCount;
    /**
     * 最后一次拉取时间
     */
    private Date lastPullTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
} 