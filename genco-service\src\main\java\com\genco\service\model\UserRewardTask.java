package com.genco.service.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户奖励/返佣任务实体，对应表eb_user_reward_task。
 * 用于记录订单返佣、邀请奖励、代理奖励等异步任务。
 */
@Data
@TableName("eb_user_reward_task")
public class UserRewardTask {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 奖励归属用户ID
     */
    private Integer userId;
    /**
     * 关联订单号，可为空
     */
    private String orderId;
    /**
     * 邀请人ID/上级ID，可为空
     */
    private Integer inviteUid;
    /**
     * 任务类型（如ORDER_REWARD、INVITE_REWARD、AGENT_REWARD）
     */
    private String taskType;
    /**
     * 任务状态（PENDING/PROCESSING/COMPLETED/FAILED）
     */
    private String status;
    /**
     * 任务上下文，JSON格式，存储扩展信息
     */
    private String context;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 订单返佣任务类型常量
     */
    public static final String TASK_TYPE_ORDER_REWARD = "ORDER_REWARD";
    /**
     * 邀请奖励任务类型常量
     */
    public static final String TASK_TYPE_INVITE_REWARD = "INVITE_REWARD";
    /**
     * 代理奖励任务类型常量
     */
    public static final String TASK_TYPE_AGENT_REWARD = "AGENT_REWARD";
} 