package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.request.StoreProductAddRequest;
import com.genco.common.request.StoreProductRequest;
import com.genco.common.response.StoreProductInfoResponse;
import com.genco.common.response.StoreProductResponse;
import org.json.JSONException;

import java.io.IOException;
import java.util.List;

/**
 * tiktok 商品处理服务
 */
public interface TikTokProductService extends IService<StoreProduct> {

    /**
     * 根据id集合获取商品信息
     *
     * @param productIds id集合
     * @return 商品信息
     */
    List<StoreProduct> getListInIds(List<Integer> productIds);


    /**
     * 产品详情
     *
     * @param productId 商品id
     * @return StoreProductResponse
     */
    StoreProductResponse getByProductId(Integer productId);


    /**
     * 新增商品
     *
     * @param request 商品请求对象
     * @return Boolean
     */
    Boolean save(StoreProductAddRequest request);

    /**
     * 更新商品信息
     *
     * @param storeProductRequest 商品参数
     * @return 更新结果
     */
    Boolean update(StoreProductAddRequest storeProductRequest);

    /**
     * 商品详情（管理端）
     *
     * @param id 商品id
     * @return StoreProductInfoResponse
     */
    StoreProductInfoResponse getInfo(Integer id);

    /**
     * 根据其他平台url导入产品信息
     *
     * @param url 待倒入平台的url
     * @param tag 待导入平台标识
     * @return 待导入的商品信息
     */
    StoreProductRequest importProductFromUrl(String url, int tag) throws IOException, JSONException;

    /**
     * 删除商品
     *
     * @param productId 商品id
     * @param type      类型：recycle——回收站 delete——彻底删除
     * @return 删除结果
     */
    Boolean deleteProduct(Integer productId, String type);
}
