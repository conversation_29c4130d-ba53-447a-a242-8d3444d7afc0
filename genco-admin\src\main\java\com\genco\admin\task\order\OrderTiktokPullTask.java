package com.genco.admin.task.order;

import com.genco.common.constants.Constants;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.utils.DateUtil;
import com.genco.common.utils.RedisUtil;
import com.genco.service.model.tiktok.OrderPullTask;
import com.genco.service.service.OrderPullTaskService;
import com.genco.service.service.OrderTaskService;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.TiktokOrderSyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * TikTok订单分页拉取定时任务
 */
@Component
@Configuration
@EnableScheduling
public class OrderTiktokPullTask {
    private static final Logger logger = LoggerFactory.getLogger(OrderTiktokPullTask.class);
    @Autowired
    private OrderPullTaskService orderPullTaskService;
    @Autowired
    private OrderTaskService orderTaskService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private TiktokOrderSyncService tiktokOrderSyncService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 每10秒扫描任务表，抢占并处理待拉取任务
     */
    @Scheduled(fixedDelay = 10000)
    public void pullTiktokOrders() {
        // 1. 获取拉取天数和最大重试次数配置
        int pullDays = 3;
        int maxRetry = 3;
        try {
            String daysStr = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ORDER_PULL_DAYS);
            String retryStr = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ORDER_PULL_MAX_RETRY);
            if (daysStr != null) pullDays = Integer.parseInt(daysStr);
            if (retryStr != null) maxRetry = Integer.parseInt(retryStr);
        } catch (Exception ignore) {
        }

        // 2. 查询待拉取任务（status=0）或可重试失败任务（status=3, retry_count<maxRetry）
        final int finalMaxRetry = maxRetry;
        List<OrderPullTask> taskList = orderPullTaskService.lambdaQuery()
                .and(q -> q.eq(OrderPullTask::getStatus, 0)
                        .or().and(i -> i.eq(OrderPullTask::getStatus, 3).lt(OrderPullTask::getRetryCount, finalMaxRetry)))
                .orderByAsc(OrderPullTask::getStartTime, OrderPullTask::getPageNo)
                .last("limit 10")
                .list();
        for (OrderPullTask task : taskList) {
            // 3. 尝试抢占任务（乐观锁，status=0/3->1）
            boolean locked = orderPullTaskService.lambdaUpdate()
                    .set(OrderPullTask::getStatus, 1)
                    .set(OrderPullTask::getLastPullTime, new Date())
                    .eq(OrderPullTask::getId, task.getId())
                    .in(OrderPullTask::getStatus, 0, 3)
                    .update();
            if (!locked) continue;
            try {
                // 4. 拉取订单并处理
                String nextPageToken = tiktokOrderSyncService.syncTiktokOrders(
                        task.getNextPageToken(), task.getStartTime(), task.getEndTime());
                // 5. 处理分页：如果有下一页，生成新任务
                if (nextPageToken != null && !nextPageToken.isEmpty()) {
                    OrderPullTask nextTask = new OrderPullTask();
                    nextTask.setStartTime(task.getStartTime());
                    nextTask.setEndTime(task.getEndTime());
                    nextTask.setPageNo(task.getPageNo() + 1);
                    nextTask.setNextPageToken(nextPageToken);
                    nextTask.setStatus(0);
                    nextTask.setRetryCount(0);
                    nextTask.setCreateTime(new Date());
                    nextTask.setUpdateTime(new Date());
                    orderPullTaskService.save(nextTask);
                }
                // 6. 标记当前任务成功
                orderPullTaskService.lambdaUpdate()
                        .set(OrderPullTask::getStatus, 2)
                        .set(OrderPullTask::getUpdateTime, new Date())
                        .eq(OrderPullTask::getId, task.getId())
                        .update();
            } catch (Exception e) {
                logger.error("TikTok订单拉取任务失败，taskId={}, pageNo={}, err={}", task.getId(), task.getPageNo(), e.getMessage(), e);
                // 7. 标记失败并递增重试次数
                int newRetryCount = task.getRetryCount() + 1;
                orderPullTaskService.lambdaUpdate()
                        .set(OrderPullTask::getStatus, 3)
                        .set(OrderPullTask::getRetryCount, newRetryCount)
                        .set(OrderPullTask::getUpdateTime, new Date())
                        .eq(OrderPullTask::getId, task.getId())
                        .update();
                // 8. 达到最大重试次数，推送到失败队列
                if (newRetryCount >= maxRetry) {
                    redisUtil.lPush(Constants.TIKTOK_ORDER_PULL_FAILED_QUEUE, task.getId());
                    logger.warn("TikTok订单拉取任务已达最大重试次数，推送到失败队列，taskId={}", task.getId());
                }
            }
        }
    }

    /**
     * 每天凌晨预生成N天的分页任务（第一页）
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void generatePullTasks() {
        int pullDays = 3;
        try {
            String daysStr = systemConfigService.getValueByKey(SysConfigConstants.TIKTOK_ORDER_PULL_DAYS);
            if (daysStr != null) pullDays = Integer.parseInt(daysStr);
        } catch (Exception ignore) {
        }
        Date end = new Date();
        Date start = DateUtil.addDays(end, -pullDays);
        // 只生成第一页任务
        OrderPullTask task = new OrderPullTask();
        task.setStartTime(start);
        task.setEndTime(end);
        task.setPageNo(1);
        task.setNextPageToken(null);
        task.setStatus(0);
        task.setRetryCount(0);
        task.setCreateTime(new Date());
        task.setUpdateTime(new Date());
        orderPullTaskService.save(task);
    }

    /**
     * 定时消费失败队列，尝试补偿拉取失败的任务
     */
    @Scheduled(fixedDelay = 60000)
    public void consumeFailedQueue() {
        String queueKey = Constants.TIKTOK_ORDER_PULL_FAILED_QUEUE;
        Long size = redisUtil.getListSize(queueKey);
        if (size == null || size == 0) return;
        for (int i = 0; i < size; i++) {
            Object taskIdObj = redisUtil.getRightPop(queueKey, 5L);
            if (taskIdObj == null) continue;
            Long taskId;
            try {
                taskId = Long.valueOf(taskIdObj.toString());
            } catch (Exception e) {
                logger.error("[补偿] 解析失败队列taskId异常: {}", taskIdObj);
                continue;
            }
            OrderPullTask task = orderPullTaskService.getById(taskId);
            if (task == null) {
                logger.warn("[补偿] 失败队列任务不存在, taskId={}", taskId);
                continue;
            }
            try {
                // 再次尝试拉取
                String nextPageToken = tiktokOrderSyncService.syncTiktokOrders(
                        task.getNextPageToken(), task.getStartTime(), task.getEndTime());
                // 分页处理（如有需要）
                if (nextPageToken != null && !nextPageToken.isEmpty()) {
                    OrderPullTask nextTask = new OrderPullTask();
                    nextTask.setStartTime(task.getStartTime());
                    nextTask.setEndTime(task.getEndTime());
                    nextTask.setPageNo(task.getPageNo() + 1);
                    nextTask.setNextPageToken(nextPageToken);
                    nextTask.setStatus(0);
                    nextTask.setRetryCount(0);
                    nextTask.setCreateTime(new Date());
                    nextTask.setUpdateTime(new Date());
                    orderPullTaskService.save(nextTask);
                }
                // 更新当前任务为成功
                orderPullTaskService.lambdaUpdate()
                        .set(OrderPullTask::getStatus, 2)
                        .set(OrderPullTask::getUpdateTime, new Date())
                        .eq(OrderPullTask::getId, task.getId())
                        .update();
                logger.info("[补偿] 失败任务补偿成功, taskId={}", taskId);
            } catch (Exception e) {
                // 补偿失败，重新放回队列
                redisUtil.lPush(queueKey, taskId);
                logger.error("[补偿] 失败任务补偿再次失败, taskId={}, err={}", taskId, e.getMessage(), e);
            }
        }
    }

    /**
     * 人工触发失败任务补偿拉取
     *
     * @param taskId 任务ID
     * @return true-补偿成功，false-失败
     */
    public boolean manualRetryFailedTask(Long taskId) {
        return orderPullTaskService.manualRetry(taskId);
    }
} 