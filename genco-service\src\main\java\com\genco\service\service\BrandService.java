package com.genco.service.service;

import com.genco.common.page.CommonPage;
import com.genco.common.request.PageParamRequest;
import com.genco.common.request.StoreBrandUpdateRequest;
import com.genco.common.response.BrandUpdateResult;
import com.genco.common.response.StoreBrandResponse;

import java.util.List;

public interface BrandService {

    /**
     * 品牌列表
     *
     * @return CommonPage
     */
    CommonPage<StoreBrandResponse> getList(Integer type, String keywords, PageParamRequest pageParamRequest);

    /**
     * 品牌详情
     *
     * @return CommonPage
     */
    StoreBrandResponse getDetail(String code);

    /**
     * 更新品牌信息
     *
     * @param request 品牌更新请求对象
     * @return 是否更新成功
     */
    Boolean updateBrand(StoreBrandUpdateRequest request);

    /**
     * 批量更新品牌信息
     *
     * @param requests 品牌更新请求对象列表
     * @return 每个品牌的更新结果
     */
    List<BrandUpdateResult> batchUpdateBrand(List<StoreBrandUpdateRequest> requests);

    /**
     * 新增品牌信息
     *
     * @param request 品牌新增请求对象
     * @return 新增品牌ID
     */
    Integer addBrand(StoreBrandUpdateRequest request);

    /**
     * 根据ID查询品牌信息
     *
     * @param id 品牌ID
     * @return 品牌信息
     */
    StoreBrandResponse getById(Integer id);

    /**
     * 逻辑删除品牌
     * @param id 品牌ID
     * @return 是否删除成功
     */
    Boolean deleteBrand(Integer id);
} 