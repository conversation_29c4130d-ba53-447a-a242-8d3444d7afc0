package com.genco.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.genco.common.constants.SysConfigConstants;
import com.genco.common.model.order.StoreOrder;
import com.genco.common.model.order.StoreOrderInfo;
import com.genco.common.model.product.StoreProduct;
import com.genco.common.response.ProductShareLinkResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202405Api;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202410Api;
import tiktokshop.open.sdk_java.api.AffiliateCreatorV202501Api;
import tiktokshop.open.sdk_java.invoke.ApiClient;
import tiktokshop.open.sdk_java.invoke.ApiResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.AddShowcaseProductsRequestBody;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.AddShowcaseProductsResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.AddShowcaseProductsResponseData;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202405.GetShowcaseProductsResponse;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202410.*;
import tiktokshop.open.sdk_java.model.AffiliateCreator.V202501.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * tiktok 处理工具类
 */
public class TikTokUtil {

    /**
     * LOGGER
     */
    private static final Logger logger = LoggerFactory.getLogger(TikTokUtil.class);

    public static void main(String[] args) {
//        for (int i = 0; i < 5; i++) {
//        genShareUrl();
//        }
//        getOpenProductDetail();
//        addShowCaseProduct();
//        getCreatorOrders();
//        System.out.println(Integer.valueOf("default-tag"));
    }

    public static void genShareUrl() {
        String service_id = "7509993722005899064";
        String appKey = "6gd0u0snl5hku";
        String appSecret = "52b0e81620cb2f33716bfd8f4db20bb9dca0d93b";
        String authCode = "ROW_iJhWVQAAAACRzra681oW-_yLgkKGLcgBB5Biuu3caNHVPgL05Ni3XgwllYoyy4vhc2AirzuBY78QEgw-zz7UTYYQADkQmp8e";
//        String accessToken = "ROW_nwo0_gAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwe2BEY-mudZ5b0CgeOMaZ3c_irEqN54vFE2mPelRYrN1mWN-suXbIJhZfUx-mj3pFQ";
        String accessToken = "ROW_B2_pqAAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwX8at10rD8abuBFZOc2FuUF9ZPkvlJkxCJ6Fpz1T3XRe8R60z5qrSXKpo1-o4p6vxA";
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202501Api affiliateCreatorV202501Api = new AffiliateCreatorV202501Api(apiClient);
        ApiResponse<GenerateAffiliateSharingLinkResponse> responseInfo = null;
        ApiResponse<CreatorSelectAffiliateProductResponse> resp = null;

        try {
            GenerateAffiliateSharingLinkRequestBody requestBody = new GenerateAffiliateSharingLinkRequestBody();
            requestBody.setChannel("default_channel");
            GenerateAffiliateSharingLinkRequestBodyMaterial material = new GenerateAffiliateSharingLinkRequestBodyMaterial();
//            material.setCampaignUrl("www.tiktok.com/product/view/1731370145462912739");
            material.setId("1731381155525789144");
            material.setType("1");
            requestBody.setMaterial(material);
            List<String> tags = new ArrayList<>();
            tags.add("4");
            requestBody.setTags(tags);
            responseInfo = affiliateCreatorV202501Api.
                    affiliateCreator202501AffiliateSharingLinksGenerateBatchPostWithHttpInfo(
                            accessToken, "application/json", requestBody);
            ProductShareLinkResponse productShareLinkResponse = null;
            if (responseInfo != null && responseInfo.getStatusCode() == 200) {
                GenerateAffiliateSharingLinkResponseData respData = responseInfo.getData().getData();
                List<GenerateAffiliateSharingLinkResponseDataAffiliateSharingLinks> affiliateSharingLinks
                        = respData.getAffiliateSharingLinks();
                for (GenerateAffiliateSharingLinkResponseDataAffiliateSharingLinks shareLink : affiliateSharingLinks) {
                    productShareLinkResponse = new ProductShareLinkResponse();
                    productShareLinkResponse.setProductName("");
                    productShareLinkResponse.setProductId(material.getId());
                    productShareLinkResponse.setProductImageUrl(material.getCampaignUrl());
                    productShareLinkResponse.setShareLink(shareLink.getAffiliateSharingLink());
                    productShareLinkResponse.setTags(shareLink.getTag());
                }
                if (CollUtil.isNotEmpty(respData.getErrors())) {

                }
            } else {
                //失败结果
            }
            System.out.println(JSONUtil.toJsonStr(productShareLinkResponse));
        } catch (Exception e) {
            System.out.println(resp);
        }
    }

    public static void getOpenProductDetail() {

        String service_id = "7509993722005899064";
        String appKey = "6gd0u0snl5hku";
        String appSecret = "52b0e81620cb2f33716bfd8f4db20bb9dca0d93b";
        String authCode = "ROW_iJhWVQAAAACRzra681oW-_yLgkKGLcgBB5Biuu3caNHVPgL05Ni3XgwllYoyy4vhc2AirzuBY78QEgw-zz7UTYYQADkQmp8e";
        String accessToken = "ROW_B2_pqAAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwX8at10rD8abuBFZOc2FuUF9ZPkvlJkxCJ6Fpz1T3XRe8R60z5qrSXKpo1-o4p6vxA";
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202501Api affiliateCreatorV202501Api = new AffiliateCreatorV202501Api(apiClient);
        ApiResponse<GenerateAffiliateSharingLinkResponse> responseInfo = null;
        ApiResponse<CreatorSelectAffiliateProductResponse> resp = null;

        try {
            CreatorSelectAffiliateProductRequestBody requestBody = new CreatorSelectAffiliateProductRequestBody();
            CreatorSelectAffiliateProductRequestBodyFilterParams params = new CreatorSelectAffiliateProductRequestBodyFilterParams();
            List<String> productIds = new ArrayList<>();
            productIds.add("1729385881063163382");
            params.setProductIds(productIds);
            requestBody.setFilterParams(params);
            resp = affiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(
                    apiClient.getTokens(), "application/json", null, 20, requestBody);

            String nextPageToken;
            List<CreatorSelectAffiliateProductResponseDataProducts> products;
            List<StoreProduct> storeProductList = new ArrayList<>();
            Integer totalCount;
            if (resp != null && resp.getStatusCode() == 200) {
                CreatorSelectAffiliateProductResponseData respData = resp.getData().getData();
                products = respData.getProducts();
                if (products != null && products.size() > 0) {
                    nextPageToken = respData.getNextPageToken();
                    totalCount = respData.getTotalCount();
                    StoreProduct storeProduct = null;
                    for (CreatorSelectAffiliateProductResponseDataProducts product : products) {
                        storeProduct = new StoreProduct();
                        storeProduct.setOutProductId(product.getId());
                        storeProduct.setChannel(SysConfigConstants.PRODUCT_CHANNEL_TIKTOK);
                        storeProduct.setImage(product.getMainImageUrl());
                        storeProduct.setBrand(product.getBrandName());
                        storeProduct.setStoreName(product.getTitle());
                        if (product.getCommission() != null && product.getCommission().getRate() != null) {
                            storeProduct.setCashBackRate(BigDecimal.valueOf(product.getCommission().getRate() * 0.001));
                        }
                        if (product.getCommission() != null && product.getCommission().getAmount() != null) {
                            storeProduct.setCashBackAmount(new BigDecimal(product.getCommission().getAmount()));
                        }
                        if (product.getPrice() != null && product.getPrice().getCeilingPrice() != null) {
                            storeProduct.setPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                            storeProduct.setMaxSalesPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                        }
                        if (product.getPrice() != null && product.getPrice().getFloorPrice() != null) {
                            storeProduct.setMinSalesPrice(new BigDecimal(product.getPrice().getFloorPrice()));
                        }
                        if (product.getShop() != null && product.getShop().getName() != null) {
                            storeProduct.setShopName(product.getShop().getName());
                        }
                        storeProduct.setStoreInfo(JSONUtil.toJsonStr(product));
                        storeProductList.add(storeProduct);
                    }
                }
            }
            System.out.print(JSONUtil.toJsonStr(storeProductList));
        } catch (
                Exception e) {
            System.out.println(resp);
        }
    }

    public static void getCreatorProductDetail() {

        String service_id = "7509993722005899064";
        String appKey = "6gd0u0snl5hku";
        String appSecret = "52b0e81620cb2f33716bfd8f4db20bb9dca0d93b";
        String authCode = "ROW_iJhWVQAAAACRzra681oW-_yLgkKGLcgBB5Biuu3caNHVPgL05Ni3XgwllYoyy4vhc2AirzuBY78QEgw-zz7UTYYQADkQmp8e";
        String accessToken = "ROW_nwo0_gAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwe2BEY-mudZ5b0CgeOMaZ3c_irEqN54vFE2mPelRYrN1mWN-suXbIJhZfUx-mj3pFQ";
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202501Api affiliateCreatorV202501Api = new AffiliateCreatorV202501Api(apiClient);
        ApiResponse<GenerateAffiliateSharingLinkResponse> responseInfo = null;
        ApiResponse<CreatorSelectAffiliateProductResponse> resp = null;

        try {
            CreatorSelectAffiliateProductRequestBody requestBody = new CreatorSelectAffiliateProductRequestBody();
            CreatorSelectAffiliateProductRequestBodyFilterParams params = new CreatorSelectAffiliateProductRequestBodyFilterParams();
            List<String> productIds = new ArrayList<>();
            productIds.add("1731370145462912739");
            params.setProductIds(productIds);
            requestBody.setFilterParams(params);
            resp = affiliateCreatorV202501Api.affiliateCreator202501SelectionProductsSearchPostWithHttpInfo(
                    apiClient.getTokens(), "application/json", null, 20, requestBody);

            String nextPageToken;
            List<CreatorSelectAffiliateProductResponseDataProducts> products;
            List<StoreProduct> storeProductList = new ArrayList<>();
            Integer totalCount;
            if (resp != null && resp.getStatusCode() == 200) {
                CreatorSelectAffiliateProductResponseData respData = resp.getData().getData();
                products = respData.getProducts();
                if (products != null && products.size() > 0) {
                    nextPageToken = respData.getNextPageToken();
                    totalCount = respData.getTotalCount();
                    StoreProduct storeProduct = null;
                    for (CreatorSelectAffiliateProductResponseDataProducts product : products) {
                        storeProduct = new StoreProduct();
                        storeProduct.setOutProductId(product.getId());
                        storeProduct.setChannel(SysConfigConstants.PRODUCT_CHANNEL_TIKTOK);
                        storeProduct.setImage(product.getMainImageUrl());
                        storeProduct.setBrand(product.getBrandName());
                        storeProduct.setStoreName(product.getTitle());
                        if (product.getCommission() != null && product.getCommission().getRate() != null) {
                            storeProduct.setCashBackRate(BigDecimal.valueOf(product.getCommission().getRate() * 0.001));
                        }
                        if (product.getCommission() != null && product.getCommission().getAmount() != null) {
                            storeProduct.setCashBackAmount(new BigDecimal(product.getCommission().getAmount()));
                        }
                        if (product.getPrice() != null && product.getPrice().getCeilingPrice() != null) {
                            storeProduct.setPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                            storeProduct.setMaxSalesPrice(new BigDecimal(product.getPrice().getCeilingPrice()));
                        }
                        if (product.getPrice() != null && product.getPrice().getFloorPrice() != null) {
                            storeProduct.setMinSalesPrice(new BigDecimal(product.getPrice().getFloorPrice()));
                        }
                        if (product.getShop() != null && product.getShop().getName() != null) {
                            storeProduct.setShopName(product.getShop().getName());
                        }
                        storeProduct.setStoreInfo(JSONUtil.toJsonStr(product));
                        storeProductList.add(storeProduct);
                    }
                }
            }
            System.out.print(JSONUtil.toJsonStr(storeProductList));
        } catch (Exception e) {
            System.out.println(resp);
        }
    }

    public static void addShowCaseProduct() {
        String service_id = "7509993722005899064";
        String appKey = "6gd0u0snl5hku";
        String appSecret = "52b0e81620cb2f33716bfd8f4db20bb9dca0d93b";
        String authCode = "ROW_iJhWVQAAAACRzra681oW-_yLgkKGLcgBB5Biuu3caNHVPgL05Ni3XgwllYoyy4vhc2AirzuBY78QEgw-zz7UTYYQADkQmp8e";
        String accessToken = "ROW_4L1gRgAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwdSN27RDYNvEm3oRlwdW2uwfiTxRkdWOSmr1C--pmfkVXKvntMsPKsjpLLkICTrtlg";
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202405Api affiliateCreatorV202405Api = new AffiliateCreatorV202405Api(apiClient);
        AddShowcaseProductsResponse responseInfo = null;

        try {
            AddShowcaseProductsRequestBody requestBody = new AddShowcaseProductsRequestBody();
            requestBody.addType("PRODUCT_LINK");
            List<String> productIds = new ArrayList<>();
//            requestBody.productIds(productIds);
            requestBody.setProductLink("https://affiliate-id.tokopedia.com/api/v1/share/AJ22lQagYSN8");
            responseInfo = affiliateCreatorV202405Api.affiliateCreator202405ShowcasesProductsAddPost(
                    accessToken, "application/json", requestBody);
            AddShowcaseProductsResponseData respData = null;
            if (responseInfo != null && responseInfo.getCode() == 200) {
                respData = responseInfo.getData();
            } else {
                //失败结果
            }
            System.out.println(JSONUtil.toJsonStr(respData));
        } catch (Exception e) {
            System.out.println(responseInfo);
        }
    }

    public static void getShowCaseProduct() {
        String service_id = "7509993722005899064";
        String appKey = "6gd0u0snl5hku";
        String appSecret = "52b0e81620cb2f33716bfd8f4db20bb9dca0d93b";
        String authCode = "ROW_iJhWVQAAAACRzra681oW-_yLgkKGLcgBB5Biuu3caNHVPgL05Ni3XgwllYoyy4vhc2AirzuBY78QEgw-zz7UTYYQADkQmp8e";
        String accessToken = "ROW_nwo0_gAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwe2BEY-mudZ5b0CgeOMaZ3c_irEqN54vFE2mPelRYrN1mWN-suXbIJhZfUx-mj3pFQ";
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202405Api affiliateCreatorV202405Api = new AffiliateCreatorV202405Api(apiClient);
        GetShowcaseProductsResponse responseInfo = null;
        try {
            responseInfo = affiliateCreatorV202405Api.affiliateCreator202405ShowcasesProductsGet(20L, "SHOWCASE",
                    accessToken, "application/json", null);
            System.out.println(JSONUtil.toJsonStr(responseInfo));
        } catch (Exception e) {
            System.out.println(responseInfo);
        }
    }

    public static void getCreatorOrders() {
        String service_id = "7509993722005899064";
        String appKey = "6gd0u0snl5hku";
        String appSecret = "52b0e81620cb2f33716bfd8f4db20bb9dca0d93b";
        String authCode = "ROW_iJhWVQAAAACRzra681oW-_yLgkKGLcgBB5Biuu3caNHVPgL05Ni3XgwllYoyy4vhc2AirzuBY78QEgw-zz7UTYYQADkQmp8e";
        String accessToken = "ROW_aOTrRAAAAACY17hRLNul1jyq7tFB0F_1i7dp60dPRyZVg_ygrMYKwRojOr1i6I_ydXtk5TODAXeFeMqppgPbLlibBrBQ8GdT2Gu6bQCY-P5e-iih171Ahg";
        ApiClient apiClient = new ApiClient();
        apiClient.setAppkey(appKey);
        apiClient.setSecret(appSecret);
        apiClient.setTokens(accessToken);

        AffiliateCreatorV202410Api affiliateCreatorV202410Api = new AffiliateCreatorV202410Api(apiClient);
        ApiResponse<GenerateAffiliateSharingLinkResponse> responseInfo = null;
        SearchCreatorAffiliateOrdersResponse resp = null;

        try {
            SearchCreatorAffiliateOrdersRequestBody requestBody = new SearchCreatorAffiliateOrdersRequestBody();
//            requestBody.setCreateTimeGe(DateUtil.getStartOfDayBefore(3));
//            requestBody.setCreateTimeLt(DateUtil.getEndOfDayBefore(3));
            resp = affiliateCreatorV202410Api.affiliateCreator202410OrdersSearchPost(
                    20L, accessToken, "application/json", null, requestBody);
            ProductShareLinkResponse productShareLinkResponse = null;
            if (resp != null && resp.getCode() == 0) {
                SearchCreatorAffiliateOrdersResponseData respData = resp.getData();
                String nextPageToken = respData.getNextPageToken();
                List<SearchCreatorAffiliateOrdersResponseDataOrders> orders = respData.getOrders();
                long totalCount = respData.getTotalCount();
                for (SearchCreatorAffiliateOrdersResponseDataOrders order : orders) {
                    StoreOrder storeOrder = new StoreOrder();
                    storeOrder.setCreateTime(DateUtil.timeStamp11ToDate(order.getCreateTime()));
                    storeOrder.setOutTradeNo(order.getId());
                    storeOrder.setDeliveryTime(DateUtil.timeStamp11ToDate(order.getDeliveryTime()));
                    storeOrder.setStatus(OrderUtil.orderStatusMapping(order.getStatus()));
                    storeOrder.setType(3);
                    //order主表落地，获取到orderId
                    Integer orderId = 0;
                    List<SearchCreatorAffiliateOrdersResponseDataOrdersSkus> skus = order.getSkus();
                    //处理每个sku的状态
                    List<StoreOrderInfo> orderInfos = new ArrayList<>();
                    for (SearchCreatorAffiliateOrdersResponseDataOrdersSkus sku : skus) {
                        StoreOrderInfo storeOrderInfo = new StoreOrderInfo();
                        storeOrderInfo.setOrderId(orderId);
                        storeOrderInfo.setTag(sku.getTag());
                        storeOrderInfo.setContentId(sku.getContentId());
                        storeOrderInfo.setPayNum(sku.getQuantity());
                        storeOrderInfo.setOutProductId(sku.getProductId());
                        storeOrderInfo.setCampaignId(sku.getCampaignId());
                        storeOrderInfo.setProductName(sku.getProductName());
                        storeOrderInfo.setProductType(5);
                        storeOrderInfo.setShopName(sku.getShopName());
                        storeOrderInfo.setRefundedQuantity(sku.getRefundedQuantity());
                        storeOrderInfo.setReturnedQuantity(sku.getReturnedQuantity());
                        //价格不能为空
                        if (sku.getPrice() != null && sku.getPrice().getAmount() != null) {
                            storeOrderInfo.setPrice(new BigDecimal(sku.getPrice().getAmount()));
                        }
                        storeOrderInfo.setPayNum(sku.getQuantity());
                        if (sku.getActualBonusCommission() != null && sku.getActualBonusCommission().getAmount() != null) {
                            storeOrderInfo.setActualBonusCommission(new BigDecimal(sku.getActualBonusCommission().getAmount()));
                        }
                        if (sku.getActualCommission() != null && sku.getActualCommission().getAmount() != null) {
                            storeOrderInfo.setActualCommission(new BigDecimal(sku.getActualCommission().getAmount()));
                        }
                        if (sku.getActualCommissionBase() != null && sku.getActualCommissionBase().getAmount() != null) {
                            storeOrderInfo.setActualCommissionBase(new BigDecimal(sku.getActualCommissionBase().getAmount()));
                        }
                        if (sku.getActualCreatorCommissionRewardFee() != null && sku.getActualCreatorCommissionRewardFee().getAmount() != null) {
                            storeOrderInfo.setActualCreatorCommissionRewardFee(new BigDecimal(sku.getActualCreatorCommissionRewardFee().getAmount()));
                        }
                        if (sku.getActualBonusCommission() != null && sku.getActualBonusCommission().getAmount() != null) {
                            storeOrderInfo.setActualBonusCommission(new BigDecimal(sku.getActualBonusCommission().getAmount()));
                        }
                        if (sku.getCommissionBonusRate() != null) {
                            storeOrderInfo.setCommissionBonusRate(new BigDecimal(sku.getCommissionBonusRate()));
                        }
                        if (sku.getEstimatedBonusCommission() != null && sku.getEstimatedBonusCommission().getAmount() != null) {
                            storeOrderInfo.setEstimatedBonusCommission(new BigDecimal(sku.getEstimatedBonusCommission().getAmount()));
                        }
                        if (sku.getEstimatedCommission() != null && sku.getEstimatedCommission().getAmount() != null) {
                            storeOrderInfo.setEstimatedCommission(new BigDecimal(sku.getEstimatedCommission().getAmount()));
                        }
                        if (sku.getEstimatedShopAdsCommission() != null && sku.getEstimatedShopAdsCommission().getAmount() != null) {
                            storeOrderInfo.setEstimatedShopAdsCommission(new BigDecimal(sku.getEstimatedShopAdsCommission().getAmount()));
                        }
                        if (sku.getEstimatedCommissionBase() != null && sku.getEstimatedCommissionBase().getAmount() != null) {
                            storeOrderInfo.setEstimatedCommissionBase(new BigDecimal(sku.getEstimatedCommissionBase().getAmount()));
                        }
                        if (sku.getEstimatedCreatorCommissionRewardFee() != null && sku.getEstimatedCreatorCommissionRewardFee().getAmount() != null) {
                            storeOrderInfo.setEstimatedCreatorCommissionRewardFee(new BigDecimal(sku.getEstimatedCreatorCommissionRewardFee().getAmount()));
                        }
                        if (sku.getShopAdsCommissionRate() != null) {
                            storeOrderInfo.setShopAdsCommissionRate(sku.getShopAdsCommissionRate());
                        }
                        orderInfos.add(storeOrderInfo);
                    }
                }
            } else {
                //失败结果
            }
            System.out.println(JSONUtil.toJsonStr(productShareLinkResponse));
        } catch (Exception e) {
            System.out.println(resp);
        }
    }
}
