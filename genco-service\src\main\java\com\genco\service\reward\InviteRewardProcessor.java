package com.genco.service.reward;

import com.genco.common.constants.Constants;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserBill;
import com.genco.service.model.UserRewardTask;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.UserBillService;
import com.genco.service.service.UserService;
import com.genco.service.utils.MemberLevelRewardUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 会员等级邀请奖励处理器
 * 基于会员等级(0-4级)的邀请奖励机制
 */
@Component
public class InviteRewardProcessor implements UserRewardTaskProcessor {

    private static final Logger logger = LoggerFactory.getLogger(InviteRewardProcessor.class);

    @Autowired
    private UserService userService;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private MemberLevelRewardUtil memberLevelRewardUtil;

    @Override
    public boolean supports(String taskType) {
        return UserRewardTask.TASK_TYPE_INVITE_REWARD.equals(taskType);
    }

    @Override
    public void process(UserRewardTask task) {
        logger.info("开始处理会员邀请奖励任务，taskId={}, userId={}, inviteUid={}",
                task.getId(), task.getUserId(), task.getInviteUid());

        // 1. 检查邀请奖励功能是否启用
        String enableConfig = systemConfigService.getValueByKey(Constants.MEMBER_INVITE_REWARD_ENABLE);
        if (!"1".equals(enableConfig)) {
            logger.info("会员邀请奖励功能未启用，跳过处理");
            return;
        }

        // 2. 获取被邀请人信息
        User invitee = userService.getInfoByUid(task.getUserId());
        if (invitee == null) {
            throw new RuntimeException("被邀请人不存在，uid=" + task.getUserId());
        }

        // 3. 获取邀请人信息
        User inviter = userService.getInfoByUid(task.getInviteUid());
        if (inviter == null) {
            throw new RuntimeException("邀请人不存在，uid=" + task.getInviteUid());
        }

        // 4. 获取会员等级
        Integer inviterGrade = memberLevelRewardUtil.getUserMemberGrade(inviter);
        Integer inviteeGrade = memberLevelRewardUtil.getUserMemberGrade(invitee);

        logger.info("邀请关系：邀请人uid={}, 等级={}, 被邀请人uid={}, 等级={}",
                inviter.getUid(), inviterGrade, invitee.getUid(), inviteeGrade);

        // 5. 处理多级邀请奖励
        processMultiLevelRewards(invitee, inviter, inviterGrade, inviteeGrade);

        logger.info("会员邀请奖励任务处理完成，taskId={}", task.getId());
    }

    /**
     * 处理多级邀请奖励
     * @param invitee 被邀请人
     * @param directInviter 直接邀请人
     * @param inviterGrade 邀请人等级
     * @param inviteeGrade 被邀请人等级
     */
    private void processMultiLevelRewards(User invitee, User directInviter, Integer inviterGrade, Integer inviteeGrade) {
        // 一级奖励：直接邀请人获得奖励
        processFirstLevelReward(invitee, directInviter, inviterGrade, inviteeGrade);

        // 二级奖励：邀请人的上级获得奖励
        if (directInviter.getSpreadUid() != null && directInviter.getSpreadUid() > 0) {
            User secondLevelInviter = userService.getInfoByUid(directInviter.getSpreadUid());
            if (secondLevelInviter != null) {
                Integer secondLevelGrade = memberLevelRewardUtil.getUserMemberGrade(secondLevelInviter);
                processSecondLevelReward(invitee, secondLevelInviter, secondLevelGrade);
            }
        }

        // 三级奖励：邀请人的上级的上级获得奖励
        if (directInviter.getSpreadUid() != null && directInviter.getSpreadUid() > 0) {
            User secondLevelInviter = userService.getInfoByUid(directInviter.getSpreadUid());
            if (secondLevelInviter != null && secondLevelInviter.getSpreadUid() != null && secondLevelInviter.getSpreadUid() > 0) {
                User thirdLevelInviter = userService.getInfoByUid(secondLevelInviter.getSpreadUid());
                if (thirdLevelInviter != null) {
                    Integer thirdLevelGrade = memberLevelRewardUtil.getUserMemberGrade(thirdLevelInviter);
                    processThirdLevelReward(invitee, thirdLevelInviter, thirdLevelGrade);
                }
            }
        }
    }

    /**
     * 处理一级邀请奖励
     * @param invitee 被邀请人
     * @param inviter 邀请人
     * @param inviterGrade 邀请人等级
     * @param inviteeGrade 被邀请人等级
     */
    private void processFirstLevelReward(User invitee, User inviter, Integer inviterGrade, Integer inviteeGrade) {
        String rewardKey = memberLevelRewardUtil.getFirstLevelRewardKey(inviterGrade, inviteeGrade);
        BigDecimal rewardAmount = getRewardAmount(rewardKey);

        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            String mark = String.format("邀请%s注册奖励-一级奖励，被邀请人:%s",
                    memberLevelRewardUtil.getMemberGradeName(inviteeGrade), invitee.getUid());
            createRewardBill(inviter, invitee, rewardAmount, mark, "invite_reward_1l");

            logger.info("发放一级邀请奖励：邀请人uid={}, 等级={}, 奖励金额={}",
                    inviter.getUid(), inviterGrade, rewardAmount);
        }
    }

    /**
     * 处理二级邀请奖励
     * @param invitee 被邀请人
     * @param inviter 二级邀请人
     * @param inviterGrade 二级邀请人等级
     */
    private void processSecondLevelReward(User invitee, User inviter, Integer inviterGrade) {
        String rewardKey = memberLevelRewardUtil.getSecondLevelRewardKey(inviterGrade);
        BigDecimal rewardAmount = getRewardAmount(rewardKey);

        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            String mark = String.format("间接邀请奖励-二级奖励，被邀请人:%s", invitee.getUid());
            createRewardBill(inviter, invitee, rewardAmount, mark, "invite_reward_2l");

            logger.info("发放二级邀请奖励：邀请人uid={}, 等级={}, 奖励金额={}",
                    inviter.getUid(), inviterGrade, rewardAmount);
        }
    }

    /**
     * 处理三级邀请奖励
     * @param invitee 被邀请人
     * @param inviter 三级邀请人
     * @param inviterGrade 三级邀请人等级
     */
    private void processThirdLevelReward(User invitee, User inviter, Integer inviterGrade) {
        String rewardKey = memberLevelRewardUtil.getThirdLevelRewardKey(inviterGrade);
        BigDecimal rewardAmount = getRewardAmount(rewardKey);

        if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
            String mark = String.format("间接邀请奖励-三级奖励，被邀请人:%s", invitee.getUid());
            createRewardBill(inviter, invitee, rewardAmount, mark, "invite_reward_3l");

            logger.info("发放三级邀请奖励：邀请人uid={}, 等级={}, 奖励金额={}",
                    inviter.getUid(), inviterGrade, rewardAmount);
        }
    }

    /**
     * 创建奖励账单
     * @param inviter 邀请人
     * @param invitee 被邀请人
     * @param amount 奖励金额
     * @param mark 备注
     * @param type 类型
     */
    private void createRewardBill(User inviter, User invitee, BigDecimal amount, String mark, String type) {
        UserBill bill = new UserBill();
        bill.setUid(inviter.getUid());
        bill.setLinkId(invitee.getUid().toString());
        bill.setPm(1); // 收入
        bill.setTitle("会员邀请奖励");
        bill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
        bill.setType(type);
        bill.setNumber(amount);
        bill.setBalance(inviter.getNowMoney() != null ? inviter.getNowMoney().add(amount) : amount);
        bill.setMark(mark);
        bill.setStatus(1);
        bill.setCreateTime(new Date());

        boolean success = userBillService.save(bill);
        if (!success) {
            throw new RuntimeException("邀请奖励账单创建失败，邀请人uid=" + inviter.getUid() + ", 被邀请人uid=" + invitee.getUid());
        }

        // 更新邀请人余额
        inviter.setNowMoney(inviter.getNowMoney() != null ? inviter.getNowMoney().add(amount) : amount);
        userService.updateById(inviter);
    }

    /**
     * 获取奖励金额
     * @param configKey 配置key
     * @return 奖励金额
     */
    private BigDecimal getRewardAmount(String configKey) {
        String configValue = systemConfigService.getValueByKey(configKey);
        try {
            return new BigDecimal(configValue);
        } catch (Exception e) {
            logger.warn("获取邀请奖励配置失败，key={}, value={}", configKey, configValue);
            return BigDecimal.ZERO;
        }
    }
}