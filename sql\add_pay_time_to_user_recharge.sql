-- 确保 eb_user_recharge 表有 pay_time 字段
-- 如果字段不存在则添加，如果存在则跳过

-- 检查字段是否存在，如果不存在则添加
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'eb_user_recharge' 
     AND COLUMN_NAME = 'pay_time') = 0,
    'ALTER TABLE `eb_user_recharge` ADD COLUMN `pay_time` timestamp(0) NULL DEFAULT NULL COMMENT ''充值支付时间'' AFTER `paid`;',
    'SELECT ''pay_time column already exists'' as message;'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加索引以提高查询性能（如果不存在）
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
     WHERE TABLE_SCHEMA = DATABASE() 
     AND TABLE_NAME = 'eb_user_recharge' 
     AND INDEX_NAME = 'idx_pay_time') = 0,
    'ALTER TABLE `eb_user_recharge` ADD INDEX `idx_pay_time` (`pay_time`) USING BTREE;',
    'SELECT ''pay_time index already exists'' as message;'
));

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt; 