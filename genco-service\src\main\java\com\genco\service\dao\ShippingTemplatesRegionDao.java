package com.genco.service.dao;

import com.genco.common.model.express.ShippingTemplatesRegion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.genco.common.request.ShippingTemplatesRegionRequest;

import java.util.List;

/**
 * Mapper 接口
 */
public interface ShippingTemplatesRegionDao extends BaseMapper<ShippingTemplatesRegion> {

    List<ShippingTemplatesRegionRequest> getListGroup(Integer tempId);
}
