package com.genco.admin.config;

import org.springframework.context.annotation.Configuration;


@Configuration
public class WhatsAppConfig {

    //    @Value("${whatsapp.api.token}")
    private String apiToken;

    //    @Value("${whatsapp.phone.number.id}")
    private String phoneNumberId;

    //    @Value("${whatsapp.api.version}")
    private String apiVersion;

    // Getters
    public String getApiToken() {
        return apiToken;
    }

    public String getPhoneNumberId() {
        return phoneNumberId;
    }

    public String getApiVersion() {
        return apiVersion;
    }

}
