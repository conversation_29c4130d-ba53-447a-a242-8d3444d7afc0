package com.genco.service.reward;

import com.genco.common.constants.Constants;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserBill;
import com.genco.service.model.UserRewardTask;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.UserBillService;
import com.genco.service.service.UserService;
import com.genco.service.utils.MemberLevelRewardUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理/合作伙伴注册奖励处理器
 * 兼容原有代理/合作伙伴模式和新的会员等级模式
 */
@Component
public class AgentRewardProcessor implements UserRewardTaskProcessor {

    private static final Logger logger = LoggerFactory.getLogger(AgentRewardProcessor.class);

    @Autowired
    private UserService userService;
    @Autowired
    private UserBillService userBillService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private MemberLevelRewardUtil memberLevelRewardUtil;

    @Override
    public boolean supports(String taskType) {
        return UserRewardTask.TASK_TYPE_AGENT_REWARD.equals(taskType);
    }

    @Override
    public void process(UserRewardTask task) {
        logger.info("开始处理代理/合作伙伴注册奖励任务，taskId={}, userId={}, inviteUid={}",
                task.getId(), task.getUserId(), task.getInviteUid());

        // 1. 获取当前用户
        User user = userService.getInfoByUid(task.getUserId());
        if (user == null) throw new RuntimeException("用户不存在，uid=" + task.getUserId());

        // 2. 判断是否使用新的会员等级模式
        String memberModeConfig = systemConfigService.getValueByKey("use_member_level_mode");
        if ("1".equals(memberModeConfig)) {
            // 使用新的会员等级模式
            processMemberLevelReward(task, user);
        } else {
            // 使用原有的代理/合作伙伴模式
            processLegacyAgentReward(task, user);
        }

        logger.info("代理/合作伙伴注册奖励任务处理完成，taskId={}", task.getId());
    }

    /**
     * 处理新的会员等级奖励模式
     */
    private void processMemberLevelReward(UserRewardTask task, User user) {
        // 获取用户会员等级
        Integer userGrade = memberLevelRewardUtil.getUserMemberGrade(user);

        // 只有达到一定等级的会员才触发代理奖励
        if (userGrade == null || userGrade < 1) {
            logger.info("用户等级不足，不触发代理奖励，uid={}, grade={}", user.getUid(), userGrade);
            return;
        }

        // 获取上级推广链路
        User up1 = user.getSpreadUid() != null ? userService.getInfoByUid(user.getSpreadUid()) : null;
        User up2 = (up1 != null && up1.getSpreadUid() != null) ? userService.getInfoByUid(up1.getSpreadUid()) : null;
        User up3 = (up2 != null && up2.getSpreadUid() != null) ? userService.getInfoByUid(up2.getSpreadUid()) : null;

        // 依次奖励上级（基于会员等级）
        rewardByMemberLevel(up1, userGrade, "高级会员注册-一级奖励", user, 1);
        rewardByMemberLevel(up2, userGrade, "高级会员注册-二级奖励", user, 2);
        rewardByMemberLevel(up3, userGrade, "高级会员注册-三级奖励", user, 3);
    }

    /**
     * 处理原有的代理/合作伙伴奖励模式（兼容性保留）
     */
    private void processLegacyAgentReward(UserRewardTask task, User user) {
        int level = user.getLevel() == null ? 0 : user.getLevel();
        boolean isAgent = (level >= 1 && level <= 3);
        boolean isPartner = (level >= 4 && level <= 6);

        if (!isAgent && !isPartner) {
            logger.info("用户不是代理或合作伙伴，跳过奖励，uid={}, level={}", user.getUid(), level);
            return;
        }

        // 获取推荐链路
        User up1 = user.getSpreadUid() != null && user.getSpreadUid() > 0 ? userService.getInfoByUid(user.getSpreadUid()) : null;
        User up2 = (up1 != null && up1.getSpreadUid() != null && up1.getSpreadUid() > 0) ? userService.getInfoByUid(up1.getSpreadUid()) : null;
        User up3 = (up2 != null && up2.getSpreadUid() != null && up2.getSpreadUid() > 0) ? userService.getInfoByUid(up2.getSpreadUid()) : null;

        // 依次奖励上级
        if (isAgent) {
            // 代理注册
            reward(up1, Constants.AGENT_INVITE_AGENT_REWARD_1L, Constants.PARTNER_INVITE_AGENT_REWARD_1L, "代理注册-一级奖励", user);
            reward(up2, Constants.AGENT_INVITE_AGENT_REWARD_2L, Constants.PARTNER_INVITE_AGENT_REWARD_2L, "代理注册-二级奖励", user);
            reward(up3, Constants.AGENT_INVITE_AGENT_REWARD_3L, Constants.PARTNER_INVITE_AGENT_REWARD_3L, "代理注册-三级奖励", user);
        } else if (isPartner) {
            // 合作伙伴注册
            reward(up1, Constants.AGENT_INVITE_PARTNER_REWARD_1L, Constants.PARTNER_INVITE_PARTNER_REWARD_1L, "合作伙伴注册-一级奖励", user);
            reward(up2, Constants.AGENT_INVITE_PARTNER_REWARD_2L, Constants.PARTNER_INVITE_PARTNER_REWARD_2L, "合作伙伴注册-二级奖励", user);
            reward(up3, Constants.AGENT_INVITE_PARTNER_REWARD_3L, Constants.PARTNER_INVITE_PARTNER_REWARD_3L, "合作伙伴注册-三级奖励", user);
        }
    }

    /**
     * 基于会员等级的奖励处理
     */
    private void rewardByMemberLevel(User up, Integer registeredUserGrade, String mark, User regUser, int level) {
        if (up == null) return;

        Integer upGrade = memberLevelRewardUtil.getUserMemberGrade(up);
        if (upGrade == null || upGrade < 1) {
            logger.info("上级用户等级不足，跳过奖励，uid={}, grade={}", up.getUid(), upGrade);
            return;
        }

        // 根据注册用户等级和奖励级别获取配置key
        String configKey;
        switch (level) {
            case 1:
                configKey = memberLevelRewardUtil.getFirstLevelRewardKey(upGrade, registeredUserGrade);
                break;
            case 2:
                configKey = memberLevelRewardUtil.getSecondLevelRewardKey(upGrade);
                break;
            case 3:
                configKey = memberLevelRewardUtil.getThirdLevelRewardKey(upGrade);
                break;
            default:
                logger.warn("不支持的奖励级别：{}", level);
                return;
        }

        BigDecimal reward = getReward(configKey);
        if (reward.compareTo(BigDecimal.ZERO) <= 0) {
            logger.info("奖励金额为0，跳过发放，configKey={}", configKey);
            return;
        }

        // 创建奖励账单
        UserBill bill = new UserBill();
        bill.setUid(up.getUid());
        bill.setLinkId(regUser.getUid().toString());
        bill.setPm(1);
        bill.setTitle("会员注册奖励");
        bill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
        bill.setType("member_agent_reward");
        bill.setNumber(reward);
        bill.setBalance(up.getNowMoney() != null ? up.getNowMoney().add(reward) : reward);
        bill.setMark(mark + "，注册用户:" + regUser.getUid() + "，等级:" + memberLevelRewardUtil.getMemberGradeName(registeredUserGrade));
        bill.setStatus(1);
        bill.setCreateTime(new Date());

        boolean success = userBillService.save(bill);
        if (!success) {
            throw new RuntimeException("会员注册奖励账单创建失败，上级uid=" + up.getUid() + ", 注册用户uid=" + regUser.getUid());
        }

        // 更新上级用户余额
        up.setNowMoney(up.getNowMoney() != null ? up.getNowMoney().add(reward) : reward);
        userService.updateById(up);

        logger.info("发放会员注册奖励：上级uid={}, 等级={}, 奖励金额={}, 配置key={}",
                up.getUid(), upGrade, reward, configKey);
    }

    /**
     * 原有的代理/合作伙伴奖励方法（兼容性保留）
     */
    private void reward(User up, String agentKey, String partnerKey, String mark, User regUser) {
        if (up == null) return;
        int upLevel = up.getLevel() == null ? 0 : up.getLevel();
        String key = (upLevel >= 1 && upLevel <= 3) ? agentKey : (upLevel >= 4 && upLevel <= 6) ? partnerKey : null;
        if (key == null) return;
        BigDecimal reward = getReward(key);
        if (reward.compareTo(BigDecimal.ZERO) <= 0) return;
        UserBill bill = new UserBill();
        bill.setUid(up.getUid());
        bill.setLinkId(regUser.getUid().toString());
        bill.setPm(1);
        bill.setTitle("代理/合作伙伴邀请奖励");
        bill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
        bill.setType("agent_invite_reward");
        bill.setNumber(reward);
        bill.setBalance(up.getNowMoney() != null ? up.getNowMoney().add(reward) : reward);
        bill.setMark(mark + "，邀请人:" + regUser.getUid());
        bill.setStatus(1);
        bill.setCreateTime(new Date());
        boolean ok = userBillService.save(bill);
        if (!ok) throw new RuntimeException("账单写入失败，uid=" + up.getUid() + ", regUid=" + regUser.getUid());

        // 更新上级用户余额
        up.setNowMoney(up.getNowMoney() != null ? up.getNowMoney().add(reward) : reward);
        userService.updateById(up);
    }

    private BigDecimal getReward(String key) {
        String val = systemConfigService.getValueByKey(key);
        try {
            return new BigDecimal(val);
        } catch (Exception e) {
            logger.warn("获取奖励配置失败，key={}, value={}", key, val);
            return BigDecimal.ZERO;
        }
    }
}