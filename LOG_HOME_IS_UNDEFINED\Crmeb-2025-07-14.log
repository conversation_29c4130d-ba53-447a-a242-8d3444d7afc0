{"@timestamp":"2025-07-14T20:51:44.117+08:00","@version":"1","message":"Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/code/easyshop/crmeb/crmeb-admin/target/classes/, file:/Users/<USER>/code/easyshop/crmeb/genco-service/target/classes/, file:/Users/<USER>/code/easyshop/crmeb/crmeb-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/Users/<USER>/.m2/repository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:51:45.092+08:00","@version":"1","message":"Starting CrmebAdminApplication on 192.168.1.4 with PID 74809 (/Users/<USER>/code/easyshop/crmeb/crmeb-admin/target/classes started by zhengfeng in /Users/<USER>/code/easyshop/crmeb)","logger_name":"com.genco.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:51:45.095+08:00","@version":"1","message":"No active profile set, falling back to default profiles: default","logger_name":"com.genco.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:51:45.096+08:00","@version":"1","message":"Loading source class com.genco.admin.CrmebAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:51:45.543+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/code/easyshop/crmeb/crmeb-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:51:45.547+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1f760b47","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:07.157+08:00","@version":"1","message":"No MyBatis mapper was found in '[com.genco.admin]' package. Please check your configuration.","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-14T20:52:09.292+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:09.338+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:09.887+08:00","@version":"1","message":"Finished Spring Data repository scanning in 356ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:21.151+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:21.212+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:21.253+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@f613067' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:21.281+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:21.363+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:24.836+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:24.838+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:24.840+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:25.171+08:00","@version":"1","message":"Tomcat initialized with port(s): 8082 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:25.272+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-8082\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:25.287+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:25.288+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:27.999+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:28.050+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:28.052+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:28.064+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 42517 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:52:35.458+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.460+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.687+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.690+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.706+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.707+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.709+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:52:35.710+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:14.930+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:22.329+08:00","@version":"1","message":"303 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:22.714+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:23.397+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:23.686+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:23.690+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:24.372+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@4eb48298, org.springframework.security.web.context.SecurityContextPersistenceFilter@7d90764a, org.springframework.security.web.header.HeaderWriterFilter@45451333, org.springframework.web.filter.CorsFilter@46d1b59, org.springframework.web.filter.CorsFilter@46d1b59, org.springframework.web.filter.CorsFilter@46d1b59, org.springframework.security.web.authentication.logout.LogoutFilter@663cf5d7, com.genco.admin.filter.JwtAuthenticationTokenFilter@65e4cb84, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@147375b3, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@69944a90, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@2d3bb944, org.springframework.security.web.session.SessionManagementFilter@3855b27e, org.springframework.security.web.access.ExceptionTranslationFilter@5981f2c6, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3c4c7e51]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:25.035+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:25.406+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:25.489+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:28.342+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:28.466+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:28.764+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.207+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.281+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.335+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.412+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.482+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.493+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.693+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.716+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.875+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:30.893+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.020+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.293+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.375+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.602+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.654+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.665+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.693+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.716+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.740+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.764+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:31.828+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.013+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.107+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.166+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.173+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.297+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.418+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.448+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.479+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.495+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.500+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.618+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.707+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.713+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.769+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.804+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:32.888+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.067+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.182+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.254+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.485+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.609+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.663+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.668+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.691+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.735+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.762+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.783+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.792+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.821+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.828+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.843+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.850+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.858+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.942+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.950+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.976+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.989+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:33.995+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.005+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.030+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.036+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.053+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.058+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.077+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.084+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.100+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.121+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.138+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.148+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.174+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.206+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.217+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.238+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.245+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.280+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.305+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.325+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.334+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.340+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.388+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.458+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.475+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.485+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.491+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.506+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.533+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.542+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.601+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.610+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.623+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.654+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.660+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.669+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.680+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.699+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.744+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.753+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.757+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.787+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.809+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.831+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.844+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.876+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.898+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.907+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.912+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.924+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.940+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.971+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:34.985+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.013+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.021+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.027+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.044+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.077+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.091+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.108+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.114+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.119+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.130+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.143+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.160+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.166+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.414+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.431+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.480+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.553+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.568+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.579+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.584+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.594+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.600+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.611+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.614+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.647+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.663+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.678+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.710+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.718+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.740+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.750+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.765+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.770+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.808+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.811+08:00","@version":"1","message":"Generating unique operation named: getUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.850+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.881+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.891+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.897+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.912+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.919+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:35.989+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.000+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.095+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.171+08:00","@version":"1","message":"Generating unique operation named: webHookUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.182+08:00","@version":"1","message":"Generating unique operation named: webHookUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.248+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.277+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-8082\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.529+08:00","@version":"1","message":"Tomcat started on port(s): 8082 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.547+08:00","@version":"1","message":"Started CrmebAdminApplication in 121.352 seconds (JVM running for 127.632)","logger_name":"com.genco.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:36.938+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:46.561+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:46.563+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:46.564+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:46.622+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:46.623+08:00","@version":"1","message":"Completed initialization in 59 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:46.833+08:00","@version":"1","message":"POST \"/api/admin/login\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:46.861+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:46.863+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:47.123+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:47.311+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [SystemAdminLoginRequest(account=admin, pwd=123456, key=null, code=null)]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:51.489+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=123456, key=null, code=null), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@7b11a244]]，cost time：********** ns，cost：3886 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:51.504+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:51.520+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@32490a5f]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:53:51.552+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:51.553+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:53:51.559+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:06.182+08:00","@version":"1","message":"GET \"/api/admin/finance/funds/monitor/list?keywords&dateLimit&linkId&type&category&page=1&limit=20\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:06.189+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.FundsMonitorController#getList(FundsMonitorRequest, PageParamRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:06.210+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:54:06.219+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:54:06.810+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.FundsMonitorController.getList(FundsMonitorRequest,PageParamRequest)，prams：[FundsMonitorRequest(keywords=, dateLimit=, title=null, category=, type=, linkId=), PageParamRequest(page=1, limit=20)]，cost time：510666205 ns，cost：510 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:54:06.821+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:06.822+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@500118ad]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:06.872+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:54:06.873+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:54:06.879+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:16.107+08:00","@version":"1","message":"Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@1f760b47, started on Mon Jul 14 20:51:45 CST 2025","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"SpringContextShutdownHook","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T20:54:16.168+08:00","@version":"1","message":"Shutting down ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T20:54:16.236+08:00","@version":"1","message":"{dataSource-1} closing ...","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:51.505+08:00","@version":"1","message":"Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_291.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/code/easyshop/crmeb/genco-admin/target/classes/, file:/Users/<USER>/code/easyshop/crmeb/genco-service/target/classes/, file:/Users/<USER>/code/easyshop/crmeb/genco-common/target/classes/, file:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/4.0.1/javax.servlet-api-4.0.1.jar, file:/Users/<USER>/.m2/repository/javax/servlet/jstl/1.2/jstl-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-jasper/9.0.33/tomcat-embed-jasper-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.33/tomcat-embed-core-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-annotations-api/9.0.33/tomcat-annotations-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.33/tomcat-embed-el-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/eclipse/jdt/ecj/3.18.0/ecj-3.18.0.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-jsp-api/9.0.33/tomcat-jsp-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-el-api/9.0.33/tomcat-el-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/tomcat-servlet-api/9.0.33/tomcat-servlet-api-9.0.33.jar, file:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar, file:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.7/velocity-1.7.jar, file:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.1/commons-collections-3.2.1.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.83/fastjson-1.2.83.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.20/druid-1.1.20.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.29/mysql-connector-java-8.0.29.jar, file:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/3.19.4/protobuf-java-3.19.4.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.12/lombok-1.18.12.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-boot-starter/3.3.1/mybatis-plus-boot-starter-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.2.6.RELEASE/spring-boot-starter-jdbc-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.4.2/HikariCP-3.4.2.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.2.5.RELEASE/spring-jdbc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus/3.3.1/mybatis-plus-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-extension/3.3.1/mybatis-plus-extension-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-core/3.3.1/mybatis-plus-core-3.3.1.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-annotation/3.3.1/mybatis-plus-annotation-3.3.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/baomidou/mybatis-plus-generator/3.3.1/mybatis-plus-generator-3.3.1.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.5.21/swagger-annotations-1.5.21.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger2/2.9.2/springfox-swagger2-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spi/2.9.2/springfox-spi-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-core/2.9.2/springfox-core-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-schema/2.9.2/springfox-schema-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-swagger-common/2.9.2/springfox-swagger-common-2.9.2.jar, file:/Users/<USER>/.m2/repository/io/springfox/springfox-spring-web/2.9.2/springfox-spring-web-2.9.2.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/20.0/guava-20.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.5.1/classmate-1.5.1.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-core/1.2.0.RELEASE/spring-plugin-core-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/plugin/spring-plugin-metadata/1.2.0.RELEASE/spring-plugin-metadata-1.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mapstruct/mapstruct/1.2.0.Final/mapstruct-1.2.0.Final.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-models/1.5.22/swagger-models-1.5.22.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.10.3/jackson-annotations-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/github/xiaoymin/swagger-bootstrap-ui/1.9.3/swagger-bootstrap-ui-1.9.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.2.6.RELEASE/spring-boot-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/net/logstash/logback/logstash-logback-encoder/5.3/logstash-logback-encoder-5.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.10.3/jackson-databind-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.10.3/jackson-core-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.2.6.RELEASE/spring-boot-starter-web-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.2.6.RELEASE/spring-boot-starter-json-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.10.3/jackson-datatype-jdk8-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.10.3/jackson-datatype-jsr310-2.10.3.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.10.3/jackson-module-parameter-names-2.10.3.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.2.6.RELEASE/spring-boot-starter-tomcat-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.33/tomcat-embed-websocket-9.0.33.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/2.2.6.RELEASE/spring-boot-starter-validation-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/2.0.2/jakarta.validation-api-2.0.2.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.18.Final/hibernate-validator-6.0.18.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.4.1.Final/jboss-logging-3.4.1.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.2.5.RELEASE/spring-web-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.2.5.RELEASE/spring-webmvc-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.2.5.RELEASE/spring-expression-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-redis/2.2.0.RELEASE/spring-boot-starter-data-redis-2.2.0.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-redis/2.2.6.RELEASE/spring-data-redis-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-keyvalue/2.2.6.RELEASE/spring-data-keyvalue-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.2.5.RELEASE/spring-tx-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-oxm/5.2.5.RELEASE/spring-oxm-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/5.2.5.RELEASE/spring-context-support-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/lettuce/lettuce-core/5.2.2.RELEASE/lettuce-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-common/4.1.48.Final/netty-common-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.48.Final/netty-handler-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.48.Final/netty-resolver-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.1.48.Final/netty-buffer-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.48.Final/netty-codec-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.48.Final/netty-transport-4.1.48.Final.jar, file:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.3.4.RELEASE/reactor-core-3.3.4.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar, file:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.7.0/commons-pool2-2.7.0.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-starter/1.2.5/pagehelper-spring-boot-starter-1.2.5.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/1.3.2/mybatis-spring-boot-starter-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/1.3.2/mybatis-spring-boot-autoconfigure-1.3.2.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper-spring-boot-autoconfigure/1.2.5/pagehelper-spring-boot-autoconfigure-1.2.5.jar, file:/Users/<USER>/.m2/repository/com/github/pagehelper/pagehelper/5.1.4/pagehelper-5.1.4.jar, file:/Users/<USER>/.m2/repository/com/github/jsqlparser/jsqlparser/1.0/jsqlparser-1.0.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/1.1.0.Final/validation-api-1.1.0.Final.jar, file:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/2.2.6.RELEASE/spring-data-commons-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.2.5.RELEASE/spring-beans-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/4.5.7/hutool-all-4.5.7.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-actuator/2.2.6.RELEASE/spring-boot-starter-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator-autoconfigure/2.2.6.RELEASE/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-actuator/2.2.6.RELEASE/spring-boot-actuator-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/io/micrometer/micrometer-core/1.3.6/micrometer-core-1.3.6.jar, file:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.1.11/HdrHistogram-2.1.11.jar, file:/Users/<USER>/.m2/repository/org/latencyutils/LatencyUtils/2.0.3/LatencyUtils-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.6/httpclient-4.5.6.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.13/httpcore-4.4.13.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.13/commons-codec-1.13.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/2.2.6.RELEASE/spring-boot-starter-aop-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.2.5.RELEASE/spring-aop-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.5/aspectjweaver-1.9.5.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.17/poi-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.17/poi-ooxml-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.17/poi-ooxml-schemas-3.17.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar, file:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.3/commons-fileupload-1.3.3.jar, file:/Users/<USER>/.m2/repository/commons-io/commons-io/2.4/commons-io-2.4.jar, file:/Users/<USER>/.m2/repository/net/coobird/thumbnailator/0.4.8/thumbnailator-0.4.8.jar, file:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.5.0/aliyun-sdk-oss-3.5.0.jar, file:/Users/<USER>/.m2/repository/org/jdom/jdom/1.1/jdom-1.1.jar, file:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.1/jettison-1.1.jar, file:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/3.4.0/aliyun-java-sdk-core-3.4.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.0.0/aliyun-java-sdk-ram-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-sts/3.0.0/aliyun-java-sdk-sts-3.0.0.jar, file:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.2.0/aliyun-java-sdk-ecs-4.2.0.jar, file:/Users/<USER>/.m2/repository/com/qcloud/cos_api/5.6.22/cos_api-5.6.22.jar, file:/Users/<USER>/.m2/repository/joda-time/joda-time/2.10.5/joda-time-2.10.5.jar, file:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.64/bcprov-jdk15on-1.64.jar, file:/Users/<USER>/.m2/repository/com/qiniu/qiniu-java-sdk/7.2.28/qiniu-java-sdk-7.2.28.jar, file:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.8.6/gson-2.8.6.jar, file:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar, file:/Users/<USER>/.m2/repository/xml-apis/xml-apis/1.0.b2/xml-apis-1.0.b2.jar, file:/Users/<USER>/.m2/repository/com/thoughtworks/xstream/xstream/1.4.18/xstream-1.4.18.jar, file:/Users/<USER>/.m2/repository/io/github/x-stream/mxparser/1.2.2/mxparser-1.2.2.jar, file:/Users/<USER>/.m2/repository/xmlpull/xmlpull/1.1.3.1/xmlpull-1.1.3.1.jar, file:/Users/<USER>/.m2/repository/org/mongodb/mongodb-driver-core/3.8.2/mongodb-driver-core-3.8.2.jar, file:/Users/<USER>/.m2/repository/org/mongodb/bson/3.11.2/bson-3.11.2.jar, file:/Users/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.2/httpmime-4.5.2.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/core/3.3.3/core-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/google/zxing/javase/3.3.3/javase-3.3.3.jar, file:/Users/<USER>/.m2/repository/com/beust/jcommander/1.72/jcommander-1.72.jar, file:/Users/<USER>/.m2/repository/com/github/jai-imageio/jai-imageio-core/1.4.0/jai-imageio-core-1.4.0.jar, file:/Users/<USER>/.m2/repository/com/belerweb/pinyin4j/2.5.0/pinyin4j-2.5.0.jar, file:/Users/<USER>/.m2/repository/io/jsonwebtoken/jjwt/0.9.1/jjwt-0.9.1.jar, file:/Users/<USER>/.m2/repository/com/auth0/jwks-rsa/0.9.0/jwks-rsa-0.9.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/2.2.6.RELEASE/spring-boot-starter-security-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-config/5.2.2.RELEASE/spring-security-config-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-core/5.2.2.RELEASE/spring-security-core-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/security/spring-security-web/5.2.2.RELEASE/spring-security-web-5.2.2.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/tiktokshop/open-sdk-java/1.0.0/open-sdk-java-1.0.0.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/io/gsonfire/gson-fire/1.9.0/gson-fire-1.9.0.jar, file:/Users/<USER>/.m2/repository/org/openapitools/jackson-databind-nullable/0.2.6/jackson-databind-nullable-0.2.6.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar, file:/Users/<USER>/.m2/repository/javax/ws/rs/javax.ws.rs-api/2.1.1/javax.ws.rs-api-2.1.1.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.11.0/okhttp-4.11.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio/3.2.0/okio-3.2.0.jar, file:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.2.0/okio-jvm-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.3.71/kotlin-stdlib-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.3.71/kotlin-stdlib-common-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.3.71/kotlin-stdlib-jdk8-1.3.71.jar, file:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.3.71/kotlin-stdlib-jdk7-1.3.71.jar, file:/Users/<USER>/.m2/repository/com/squareup/okhttp3/logging-interceptor/4.11.0/logging-interceptor-4.11.0.jar, file:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3/json-smart-2.3.jar, file:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.2.6.RELEASE/spring-boot-starter-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.2.5.RELEASE/spring-context-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.2.6.RELEASE/spring-boot-starter-logging-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.12.1/log4j-to-slf4j-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.12.1/log4j-api-2.12.1.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.30/jul-to-slf4j-1.7.30.jar, file:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.25/snakeyaml-1.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/2.2.6.RELEASE/spring-boot-test-2.2.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.2.5.RELEASE/spring-core-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.2.5.RELEASE/spring-jcl-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-test/5.2.5.RELEASE/spring-test-5.2.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.10.8/byte-buddy-1.10.8.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]","logger_name":"org.springframework.boot.context.logging.ClasspathLoggingApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:51.749+08:00","@version":"1","message":"Starting CrmebAdminApplication on 192.168.1.4 with PID 75212 (/Users/<USER>/code/easyshop/crmeb/genco-admin/target/classes started by zhengfeng in /Users/<USER>/code/easyshop/crmeb)","logger_name":"com.genco.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:51.752+08:00","@version":"1","message":"No active profile set, falling back to default profiles: default","logger_name":"com.genco.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:51.753+08:00","@version":"1","message":"Loading source class com.genco.admin.CrmebAdminApplication","logger_name":"org.springframework.boot.SpringApplication","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:51.889+08:00","@version":"1","message":"Loaded config file 'file:/Users/<USER>/code/easyshop/crmeb/genco-admin/target/classes/application.yml' (classpath:/application.yml)","logger_name":"org.springframework.boot.context.config.ConfigFileApplicationListener","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:51.890+08:00","@version":"1","message":"Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3576ddc2","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:55.064+08:00","@version":"1","message":"No MyBatis mapper was found in '[com.genco.admin]' package. Please check your configuration.","logger_name":"org.mybatis.spring.mapper.ClassPathMapperScanner","thread_name":"main","level":"WARN","level_value":30000,"tags":["MYBATIS"]}
{"@timestamp":"2025-07-14T21:08:55.538+08:00","@version":"1","message":"Multiple Spring Data modules found, entering strict repository configuration mode!","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:55.545+08:00","@version":"1","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:55.630+08:00","@version":"1","message":"Finished Spring Data repository scanning in 37ms. Found 0 Redis repository interfaces.","logger_name":"org.springframework.data.repository.config.RepositoryConfigurationDelegate","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:58.566+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration' of type [org.springframework.security.config.annotation.configuration.ObjectPostProcessorConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:58.583+08:00","@version":"1","message":"Bean 'objectPostProcessor' of type [org.springframework.security.config.annotation.configuration.AutowireBeanFactoryObjectPostProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:58.590+08:00","@version":"1","message":"Bean 'org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler@50fdf44f' of type [org.springframework.security.access.expression.method.DefaultMethodSecurityExpressionHandler] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:58.598+08:00","@version":"1","message":"Bean 'org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration' of type [org.springframework.security.config.annotation.method.configuration.GlobalMethodSecurityConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:58.621+08:00","@version":"1","message":"Bean 'methodSecurityMetadataSource' of type [org.springframework.security.access.method.DelegatingMethodSecurityMetadataSource] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)","logger_name":"org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:59.534+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:59.535+08:00","@version":"1","message":"Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.2.6.RELEASE/spring-boot-2.2.6.RELEASE.jar","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:59.536+08:00","@version":"1","message":"None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:08:59.632+08:00","@version":"1","message":"Tomcat initialized with port(s): 8082 (http)","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:59.665+08:00","@version":"1","message":"Initializing ProtocolHandler [\"http-nio-8082\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:59.667+08:00","@version":"1","message":"Starting service [Tomcat]","logger_name":"org.apache.catalina.core.StandardService","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:08:59.667+08:00","@version":"1","message":"Starting Servlet engine: [Apache Tomcat/9.0.33]","logger_name":"org.apache.catalina.core.StandardEngine","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:00.667+08:00","@version":"1","message":"At least one JAR was scanned for TLDs yet contained no TLDs. Enable debug logging for this logger for a complete list of JARs that were scanned but no TLDs were found in them. Skipping unneeded JARs during scanning can improve startup time and JSP compilation time.","logger_name":"org.apache.jasper.servlet.TldScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:00.684+08:00","@version":"1","message":"Initializing Spring embedded WebApplicationContext","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:00.685+08:00","@version":"1","message":"Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:00.686+08:00","@version":"1","message":"Root WebApplicationContext: initialization completed in 8796 ms","logger_name":"org.springframework.web.context.ContextLoader","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:03.056+08:00","@version":"1","message":"Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/api/admin/*, /api/front/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647, jwtAuthenticationTokenFilter urls=[/*] order=2147483647","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.057+08:00","@version":"1","message":"Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]","logger_name":"org.springframework.boot.web.servlet.ServletContextInitializerBeans","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.140+08:00","@version":"1","message":"Filter 'webMvcMetricsFilter' configured for use","logger_name":"org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.141+08:00","@version":"1","message":"Filter 'requestContextFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.143+08:00","@version":"1","message":"Filter 'corsFilter' configured for use","logger_name":"org.springframework.web.filter.CorsFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.143+08:00","@version":"1","message":"Filter 'characterEncodingFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.144+08:00","@version":"1","message":"Filter 'springSecurityFilterChain' configured for use","logger_name":"org.springframework.boot.web.servlet.DelegatingFilterProxyRegistrationBean$1","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:03.145+08:00","@version":"1","message":"Filter 'formContentFilter' configured for use","logger_name":"org.springframework.boot.web.servlet.filter.OrderedFormContentFilter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:23.547+08:00","@version":"1","message":"支付策略初始化完成，支持的支付类型: [offline, alipay, weixin, xendit, yue, haipay]","logger_name":"com.genco.service.service.impl.PaymentStrategyFactory","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:28.354+08:00","@version":"1","message":"303 mappings in 'requestMappingHandlerMapping'","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:28.687+08:00","@version":"1","message":"Exposing 2 endpoint(s) beneath base path '/actuator'","logger_name":"org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:29.223+08:00","@version":"1","message":"Mapped URL path [/v2/api-docs] onto method [springfox.documentation.swagger2.web.Swagger2Controller#getDocumentation(String, HttpServletRequest)]","logger_name":"springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:29.352+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:29.354+08:00","@version":"1","message":"Initializing ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:30.133+08:00","@version":"1","message":"Creating filter chain: any request, [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@637d111d, org.springframework.security.web.context.SecurityContextPersistenceFilter@1253b822, org.springframework.security.web.header.HeaderWriterFilter@683ed81b, org.springframework.web.filter.CorsFilter@819fb19, org.springframework.web.filter.CorsFilter@819fb19, org.springframework.web.filter.CorsFilter@819fb19, org.springframework.security.web.authentication.logout.LogoutFilter@52035328, com.genco.admin.filter.JwtAuthenticationTokenFilter@7afbf2a0, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@3cc2e3e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@5807ea46, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@3a917017, org.springframework.security.web.session.SessionManagementFilter@5bdb6ea8, org.springframework.security.web.access.ExceptionTranslationFilter@62c47480, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@5bb39285]","logger_name":"org.springframework.security.web.DefaultSecurityFilterChain","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:30.935+08:00","@version":"1","message":"ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:31.290+08:00","@version":"1","message":"Patterns [/webjars/**, /**, /doc.html, /crmebimage/**] in 'resourceHandlerMapping'","logger_name":"org.springframework.web.servlet.handler.SimpleUrlHandlerMapping","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:31.368+08:00","@version":"1","message":"ControllerAdvice beans: 1 @ExceptionHandler, 1 ResponseBodyAdvice","logger_name":"org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver","thread_name":"main","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:09:33.763+08:00","@version":"1","message":"Context refreshed","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:33.884+08:00","@version":"1","message":"Found 2 custom documentation plugin(s)","logger_name":"springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:34.153+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.327+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.392+08:00","@version":"1","message":"Generating unique operation named: getByIdsUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.464+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.543+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.632+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.658+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.894+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:35.920+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.016+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.035+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.129+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.367+08:00","@version":"1","message":"Generating unique operation named: updatePhoneUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.434+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.553+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.584+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.606+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.647+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.656+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.686+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.712+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.766+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:36.945+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.037+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.089+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.093+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.249+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.390+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.584+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.690+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.702+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.707+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.912+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.963+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:37.969+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.057+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.093+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.214+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.395+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.538+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.609+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.775+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.877+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.931+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.934+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.955+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:38.986+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.233+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.268+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_7","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.292+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.341+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.347+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.366+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.372+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_8","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.378+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.482+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.487+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.518+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.523+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_9","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.539+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.548+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.576+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.584+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.605+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.609+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_10","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.626+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.635+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.659+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.682+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.689+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.698+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_11","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.725+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.752+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.768+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.778+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.783+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_12","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.812+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.838+08:00","@version":"1","message":"Generating unique operation named: getListTreeUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.858+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.867+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.882+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_5","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:39.923+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.001+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.007+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.025+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.030+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.037+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_13","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.066+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.083+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.091+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.096+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.113+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_14","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.145+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.152+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.176+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.182+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.210+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.262+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.271+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.286+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.317+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.342+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.353+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.375+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_15","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.407+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.431+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.440+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.455+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.459+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.470+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_16","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.499+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.515+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.526+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.541+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.545+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.552+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_17","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.579+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_29","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.602+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.611+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.628+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.634+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_3","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.641+08:00","@version":"1","message":"Generating unique operation named: deleteUsingPOST_2","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.661+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_30","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.671+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:40.691+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.033+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_31","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.041+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.120+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_23","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.209+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_32","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.235+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_24","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.239+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingPOST_6","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.255+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_18","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.264+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_33","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.272+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.289+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.295+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_25","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.343+08:00","@version":"1","message":"Generating unique operation named: getListUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.374+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_34","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.400+08:00","@version":"1","message":"Generating unique operation named: balanceUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.456+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_35","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.463+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_19","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.514+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_36","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.522+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.542+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.549+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_26","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.614+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_20","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.639+08:00","@version":"1","message":"Generating unique operation named: getUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.701+08:00","@version":"1","message":"Generating unique operation named: deleteUsingGET_21","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.747+08:00","@version":"1","message":"Generating unique operation named: getListUsingGET_37","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.755+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.772+08:00","@version":"1","message":"Generating unique operation named: infoUsingGET_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.783+08:00","@version":"1","message":"Generating unique operation named: saveUsingPOST_22","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.800+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_27","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.804+08:00","@version":"1","message":"Generating unique operation named: updateUsingPOST_28","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.808+08:00","@version":"1","message":"Generating unique operation named: updateStatusUsingGET_4","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.896+08:00","@version":"1","message":"Scanning for api listing references","logger_name":"springfox.documentation.spring.web.scanners.ApiListingReferenceScanner","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.973+08:00","@version":"1","message":"Generating unique operation named: webHookUsingPOST_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:41.977+08:00","@version":"1","message":"Generating unique operation named: webHookUsingGET_1","logger_name":"springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:42.056+08:00","@version":"1","message":"Initializing ExecutorService","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:42.090+08:00","@version":"1","message":"Starting ProtocolHandler [\"http-nio-8082\"]","logger_name":"org.apache.coyote.http11.Http11NioProtocol","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:42.413+08:00","@version":"1","message":"Tomcat started on port(s): 8082 (http) with context path ''","logger_name":"org.springframework.boot.web.embedded.tomcat.TomcatWebServer","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:42.435+08:00","@version":"1","message":"Started CrmebAdminApplication in 52.644 seconds (JVM running for 53.862)","logger_name":"com.genco.admin.CrmebAdminApplication","thread_name":"main","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:09:43.069+08:00","@version":"1","message":"{dataSource-1} inited","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"crmeb-scheduled-task-pool-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.003+08:00","@version":"1","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","logger_name":"org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.004+08:00","@version":"1","message":"Initializing Servlet 'dispatcherServlet'","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.005+08:00","@version":"1","message":"Detected StandardServletMultipartResolver","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:13.021+08:00","@version":"1","message":"enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:13.022+08:00","@version":"1","message":"Completed initialization in 17 ms","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.153+08:00","@version":"1","message":"GET \"/api/admin/finance/funds/monitor/list?keywords&dateLimit&linkId&type&category&page=1&limit=20\", parameters={masked}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:13.160+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.FundsMonitorController#getList(FundsMonitorRequest, PageParamRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:13.169+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.244+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.523+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.FundsMonitorController.getList(FundsMonitorRequest,PageParamRequest)，prams：[FundsMonitorRequest(keywords=, dateLimit=, title=null, category=, type=, linkId=), PageParamRequest(page=1, limit=20)]，cost time：150086711 ns，cost：150 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.556+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:13.557+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@268e69c8]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:13.578+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.579+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-1","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:13.579+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-1","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:20.173+08:00","@version":"1","message":"POST \"/api/admin/login\", parameters={}","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:20.175+08:00","@version":"1","message":"Mapped to com.genco.admin.controller.AdminLoginController#SystemAdminLogin(SystemAdminLoginRequest, HttpServletRequest)","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:20.175+08:00","@version":"1","message":"[DIGEST] 请求开始 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, URI: {}, 方法: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:20.176+08:00","@version":"1","message":"[DETAIL] 请求详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:20.182+08:00","@version":"1","message":"Read \"application/json;charset=UTF-8\" to [SystemAdminLoginRequest(account=admin, pwd=123456, key=null, code=null)]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:21.298+08:00","@version":"1","message":"Controller method：CommonResult com.genco.admin.controller.AdminLoginController.SystemAdminLogin(SystemAdminLoginRequest,HttpServletRequest)，prams：[SystemAdminLoginRequest(account=admin, pwd=123456, key=null, code=null), SecurityContextHolderAwareRequestWrapper[ org.springframework.security.web.header.HeaderWriterFilter$HeaderWriterRequest@38d26d0f]]，cost time：********** ns，cost：1106 ms","logger_name":"com.genco.admin.acpect.ControllerAspect","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:21.298+08:00","@version":"1","message":"Using 'application/json', given [*/*] and supported [application/json]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:21.299+08:00","@version":"1","message":"Writing [com.genco.common.response.CommonResult@10178ab]","logger_name":"org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:21.301+08:00","@version":"1","message":"[DIGEST] 请求结束 - TraceId: {}, 类名: {}, 方法名: {}, 用户ID: {}, 是否成功: {}, 响应时间: {}ms, 状态码: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:21.302+08:00","@version":"1","message":"[DETAIL] 响应详情: {}","logger_name":"com.genco.common.interceptor.ControllerLogInterceptor","thread_name":"http-nio-8082-exec-2","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:21.302+08:00","@version":"1","message":"Completed 200 OK","logger_name":"org.springframework.web.servlet.DispatcherServlet","thread_name":"http-nio-8082-exec-2","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:25.697+08:00","@version":"1","message":"Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3576ddc2, started on Mon Jul 14 21:08:51 CST 2025","logger_name":"org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext","thread_name":"SpringContextShutdownHook","level":"DEBUG","level_value":10000}
{"@timestamp":"2025-07-14T21:11:25.714+08:00","@version":"1","message":"Shutting down ExecutorService 'taskExecutor'","logger_name":"org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:25.735+08:00","@version":"1","message":"{dataSource-1} closing ...","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
{"@timestamp":"2025-07-14T21:11:25.749+08:00","@version":"1","message":"{dataSource-1} closed","logger_name":"com.alibaba.druid.pool.DruidDataSource","thread_name":"SpringContextShutdownHook","level":"INFO","level_value":20000}
