package com.genco.common.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付回调请求对象
 */
@Data
@ApiModel(value = "PayCallbackRequest", description = "支付回调请求对象")
public class PayCallbackRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "外部交易单号")
    private String outTradeNo;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "支付状态，true为成功，false为失败")
    private Boolean status;

    @ApiModelProperty(value = "支付时间")
    private Date payTime;

    @ApiModelProperty(value = "支付渠道，如xendit、haipay、weixin等")
    private String payType;

    @ApiModelProperty(value = "支付渠道")
    private String payChannel;
} 